<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class anak extends Model
{
    protected $table = 'anak';
    protected $fillable = ['father_id', 'nama', 'jantina', 'tarikh_lahir', 'no_hp', 'status', 'pendidikan', 'pekerjaan', 'agama', 'status_oku'];

    public function keluarga()
    {
        return $this->belongsTo(Keluarga::class);
    }

    public function ipt()
    {
        return $this->hasOne(Ipt::class);
    }
}
