import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { DatePicker } from '@/components/ui/date-picker';

type Child = {
    nama: string;
    jantina: string;
    tarikh_lahir: string;
    no_hp: string;
    status: string;
    pendidikan: string;
    pekerjaan: string;
    pekerjaan_lain: string;
    agama: string;
    agama_lain: string;
    status_oku: boolean;
    sedang_belajar_ipt: boolean;
    tajaan: string;
};

interface AnakFormProps {
    children: Child[];
    setChildren: (children: Child[]) => void;
    setData: (key: string, value: any) => void;
    calculateAge: (date: string) => string | number;
}

export default function AnakForm({ children, setChildren, setData, calculateAge }: AnakFormProps) {
    const addChild = () => {
        const newChild = { 
            nama: '', 
            jantina: '', 
            tarikh_lahir: '', 
            no_hp: '', 
            status: '', 
            pendidikan: '', 
            pekerjaan: '', 
            pekerjaan_lain: '',
            agama: '', 
            agama_lain: '',
            status_oku: false,
            sedang_belajar_ipt: false,
            tajaan: ''
        };
        setChildren([...children, newChild]);
        setData('children', [...children, newChild]);
    };

    const removeChild = (index: number) => {
        const newChildren = children.filter((_, i) => i !== index);
        setChildren(newChildren);
        setData('children', newChildren);
    };

    const updateChild = (index: number, field: keyof Child, value: string | boolean) => {
        const newChildren = [...children];
        newChildren[index] = { ...newChildren[index], [field]: value };
        setChildren(newChildren);
        setData('children', newChildren);
    };

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <div>
                    <p className="text-sm text-muted-foreground">
                        Sila isi maklumat anak-anak anda dengan lengkap
                    </p>
                </div>
                <Button type="button" variant="outline" onClick={addChild}>
                    + Tambah Anak
                </Button>
            </div>
            
            {children.map((child, index) => (
                <div key={index} className="space-y-4 p-4 border rounded-lg bg-card">
                    <div className="flex justify-between items-center">
                        <h4 className="font-medium">Anak {index + 1}</h4>
                        {index > 0 && (
                            <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                onClick={() => removeChild(index)}
                            >
                                Padam
                            </Button>
                        )}
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-2">
                        <div className="grid gap-2">
                            <Label htmlFor={`child_nama_${index}`}>Nama</Label>
                            <Input
                                id={`child_nama_${index}`}
                                value={child.nama}
                                onChange={e => updateChild(index, 'nama', e.target.value)}
                                placeholder="Contoh: Ali Bin Ahmad"
                            />
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor={`child_jantina_${index}`}>Jantina</Label>
                            <Select
                                value={child.jantina}
                                onValueChange={(value) => updateChild(index, 'jantina', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Pilih jantina" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="lelaki">Lelaki</SelectItem>
                                    <SelectItem value="perempuan">Perempuan</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor={`child_tarikh_lahir_${index}`}>Tarikh Lahir</Label>
                            <DatePicker
                                id={`child_tarikh_lahir_${index}`}
                                value={child.tarikh_lahir}
                                onChange={(date) => updateChild(index, 'tarikh_lahir', date)}
                                maxDate={new Date()}
                                placeholder="Pilih tarikh lahir"
                            />
                            {child.tarikh_lahir && (
                                <p className="text-sm text-muted-foreground">
                                    Umur: {calculateAge(child.tarikh_lahir)} tahun
                                </p>
                            )}
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor={`child_no_hp_${index}`}>Nombor Telefon</Label>
                            <Input
                                id={`child_no_hp_${index}`}
                                value={child.no_hp}
                                onChange={e => updateChild(index, 'no_hp', e.target.value)}
                                placeholder="Contoh: 0123456789"
                                maxLength={15}
                            />
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor={`child_status_${index}`}>Status</Label>
                            <Select
                                value={child.status}
                                onValueChange={(value) => updateChild(index, 'status', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Pilih status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="pelajar">Pelajar</SelectItem>
                                    <SelectItem value="bekerja">Bekerja</SelectItem>
                                    <SelectItem value="menganggur">Menganggur</SelectItem>
                                    <SelectItem value="bujang">Bujang</SelectItem>
                                    <SelectItem value="berkahwin">Berkahwin</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor={`child_pendidikan_${index}`}>Tahap Pendidikan</Label>
                            <Select
                                value={child.pendidikan}
                                onValueChange={(value) => updateChild(index, 'pendidikan', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Pilih tahap pendidikan" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="tiada_pendidikan">Tiada Pendidikan</SelectItem>
                                    <SelectItem value="sekolah_dewasa">Sekolah Dewasa</SelectItem>
                                    <SelectItem value="sekolah_pondok_agama">Sekolah Pondok/Agama</SelectItem>
                                    <SelectItem value="tadika_prasekolah">Tadika/PraSekolah</SelectItem>
                                    <SelectItem value="sekolah_rendah_kebangsaan">Sekolah Rendah Kebangsaan</SelectItem>
                                    <SelectItem value="pt3_pmr_srp">PT3/PMR/SRP</SelectItem>
                                    <SelectItem value="spm">SPM</SelectItem>
                                    <SelectItem value="stpm">STPM</SelectItem>
                                    <SelectItem value="sijil_kemahiran_bertauliah">Sijil Kemahiran Bertauliah</SelectItem>
                                    <SelectItem value="diploma_setaraf">Diploma/Setaraf</SelectItem>
                                    <SelectItem value="ijazah_ke_atas">Ijazah ke Atas</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        {child.tarikh_lahir && Number(calculateAge(child.tarikh_lahir)) >= 18 && (
                            <div className="grid gap-2">
                                <Label htmlFor={`child_pekerjaan_${index}`}>Pekerjaan</Label>
                                <Select
                                    value={child.pekerjaan}
                                    onValueChange={(value) => {
                                        updateChild(index, 'pekerjaan', value);
                                        if (value !== 'lain_lain') {
                                            updateChild(index, 'pekerjaan_lain', '');
                                        }
                                    }}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Pilih pekerjaan" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="penjawat_awam">Penjawat Awam</SelectItem>
                                        <SelectItem value="pekerja_swasta">Pekerja Swasta</SelectItem>
                                        <SelectItem value="petani">Petani</SelectItem>
                                        <SelectItem value="nelayan">Nelayan</SelectItem>
                                        <SelectItem value="usahawan">Usahawan</SelectItem>
                                        <SelectItem value="surirumah_sepenuh_masa">Surirumah Sepenuh Masa</SelectItem>
                                        <SelectItem value="pesara">Pesara</SelectItem>
                                        <SelectItem value="penganggur_belia">Penganggur - Belia (Tiada Kemahiran)</SelectItem>
                                        <SelectItem value="penganggur_graduan">Penganggur - Graduan</SelectItem>
                                        <SelectItem value="lain_lain">Lain-lain (Nyatakan)</SelectItem>
                                    </SelectContent>
                                </Select>
                                {child.pekerjaan === 'lain_lain' && (
                                    <Input
                                        id={`child_pekerjaan_lain_${index}`}
                                        value={child.pekerjaan_lain}
                                        onChange={e => updateChild(index, 'pekerjaan_lain', e.target.value)}
                                        placeholder="Nyatakan pekerjaan lain"
                                        className="mt-2"
                                    />
                                )}
                            </div>
                        )}

                        <div className="grid gap-2">
                            <Label htmlFor={`child_agama_${index}`}>Agama</Label>
                            <Select
                                value={child.agama}
                                onValueChange={(value) => {
                                    updateChild(index, 'agama', value);
                                    if (value !== 'lain-lain') {
                                        updateChild(index, 'agama_lain', '');
                                    }
                                }}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Pilih agama" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="islam">Islam</SelectItem>
                                    <SelectItem value="kristian">Kristian</SelectItem>
                                    <SelectItem value="buddha">Buddha</SelectItem>
                                    <SelectItem value="hindu">Hindu</SelectItem>
                                    <SelectItem value="lain-lain">Lain-lain</SelectItem>
                                </SelectContent>
                            </Select>
                            {child.agama === 'lain-lain' && (
                                <Input
                                    id={`child_agama_lain_${index}`}
                                    value={child.agama_lain}
                                    onChange={e => updateChild(index, 'agama_lain', e.target.value)}
                                    placeholder="Nyatakan agama lain"
                                    className="mt-2"
                                />
                            )}
                        </div>
                    </div>

                    <div className="flex items-center space-x-2">
                        <Checkbox
                            id={`child_status_oku_${index}`}
                            checked={child.status_oku}
                            onCheckedChange={(checked) => updateChild(index, 'status_oku', Boolean(checked))}
                        />
                        <Label htmlFor={`child_status_oku_${index}`}>Status OKU</Label>
                    </div>

                    {/* University Study Toggle */}
                    <div className="flex items-center space-x-2">
                        <Checkbox
                            id={`child_sedang_belajar_ipt_${index}`}
                            checked={child.sedang_belajar_ipt}
                            onCheckedChange={(checked) => {
                                updateChild(index, 'sedang_belajar_ipt', Boolean(checked));
                                if (!checked) {
                                    updateChild(index, 'tajaan', '');
                                }
                            }}
                        />
                        <Label htmlFor={`child_sedang_belajar_ipt_${index}`}>Sedang belajar di Institut Pengajian Tinggi (IPT)</Label>
                    </div>

                    {/* IPT Fields - Show only if studying at university */}
                    {child.sedang_belajar_ipt && (
                        <div className="mt-4 p-4 border rounded-lg bg-blue-50">
                            <h5 className="font-medium text-blue-900 mb-3">Maklumat Institut Pengajian Tinggi</h5>
                            <div className="grid gap-4 md:grid-cols-1">
                                <div className="grid gap-2">
                                    <Label htmlFor={`child_tajaan_${index}`}>Jenis Tajaan</Label>
                                    <Select
                                        value={child.tajaan}
                                        onValueChange={(value) => updateChild(index, 'tajaan', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Pilih jenis tajaan" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="swasta">Swasta</SelectItem>
                                            <SelectItem value="kerajaan">Kerajaan</SelectItem>
                                            <SelectItem value="sendiri">Sendiri</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            ))}
        </div>
    );
}
