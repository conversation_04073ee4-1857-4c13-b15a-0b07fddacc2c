import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { DatePicker } from '@/components/ui/date-picker';

type Child = {
    id?: string;
    nama: string;
    jantina: string;
    tarikh_lahir: string;
    no_hp: string;
    status: string;
    pendidikan: string;
    pekerjaan: string;
    pekerjaan_lain: string;
    agama: string;
    agama_lain: string;
    status_oku: boolean;
    sedang_belajar_ipt: boolean;
    tajaan: string;
};

interface AnakFormProps {
    children: Child[];
    setChildren: (children: Child[]) => void;
    setData: (key: string, value: unknown) => void;
    calculateAge: (date: string) => string | number;
}

export default function AnakForm({ children, setChildren, setData, calculateAge }: AnakFormProps) {
    const addChild = () => {
        const newChild = { 
            id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
            nama: '', 
            jantina: '', 
            tarikh_lahir: '', 
            no_hp: '', 
            status: '', 
            pendidikan: '', 
            pekerjaan: '', 
            pekerjaan_lain: '',
            agama: '', 
            agama_lain: '',
            status_oku: false,
            sedang_belajar_ipt: false,
            tajaan: ''
        };
        setChildren([...children, newChild]);
        setData('children', [...children, newChild]);
    };

    const removeChild = (index: number) => {
        const newChildren = children.filter((_, i) => i !== index);
        setChildren(newChildren);
        setData('children', newChildren);
    };

    const updateChild = (index: number, field: keyof Child, value: string | boolean) => {
        const newChildren = children.map((child, i) =>
            i === index ? { ...child, [field]: value } : child
        );
        setChildren(newChildren);
        setData('children', newChildren);
    };

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center my-4">
                <div>
                    <p className="text-sm text-muted-foreground">
                        Sila isi maklumat anak-anak anda dengan lengkap
                    </p>
                </div>
                <Button type="button" variant="default" onClick={addChild} >
                    + Tambah Anak
                </Button>
            </div>
            
            {children.map((child, index) => (
                <div key={child.id || index} className="space-y-4 p-4 border rounded-lg bg-card">
                    <div className="flex justify-between items-center">
                        <h4 className="font-medium">Anak {index + 1}</h4>
                        {index > 0 && (
                            <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                onClick={() => removeChild(index)}
                            >
                                Padam
                            </Button>
                        )}
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-2">
                        <div className="grid gap-2">
                            <Label htmlFor={`child_nama_${index}`}>Nama</Label>
                            <Input
                                id={`child_nama_${index}`}
                                value={child.nama}
                                onChange={e => updateChild(index, 'nama', e.target.value)}
                                placeholder="Contoh: Ali Bin Ahmad"
                            />
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor={`child_jantina_${index}`}>Jantina</Label>
                            <select
                                id={`child_jantina_${index}`}
                                value={child.jantina || ""}
                                onChange={(e) => updateChild(index, 'jantina', e.target.value)}
                                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            >
                                <option value="">Pilih jantina</option>
                                <option value="lelaki">Lelaki</option>
                                <option value="perempuan">Perempuan</option>
                            </select>
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor={`child_tarikh_lahir_${index}`}>Tarikh Lahir</Label>
                            <DatePicker
                                id={`child_tarikh_lahir_${index}`}
                                value={child.tarikh_lahir}
                                onChange={(date) => updateChild(index, 'tarikh_lahir', date)}
                                maxDate={new Date()}
                                placeholder="Pilih tarikh lahir"
                            />
                            {child.tarikh_lahir && (
                                <p className="text-sm text-muted-foreground">
                                    Umur: {calculateAge(child.tarikh_lahir)} tahun
                                </p>
                            )}
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor={`child_no_hp_${index}`}>Nombor Telefon (Pilihan)</Label>
                            <Input
                                id={`child_no_hp_${index}`}
                                value={child.no_hp}
                                onChange={e => updateChild(index, 'no_hp', e.target.value)}
                                placeholder="Contoh: 0123456789 (tidak wajib)"
                                maxLength={15}
                            />
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor={`child_status_${index}`}>Status</Label>
                            <select
                                id={`child_status_${index}`}
                                value={child.status || ""}
                                onChange={(e) => updateChild(index, 'status', e.target.value)}
                                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            >
                                <option value="">Pilih status (tidak wajib)</option>
                                <option value="pelajar">Pelajar</option>
                                <option value="bekerja">Bekerja</option>
                                <option value="menganggur">Menganggur</option>
                                <option value="bujang">Bujang</option>
                                <option value="berkahwin">Berkahwin</option>
                                <option value="balu">Balu</option>
                                <option value="janda">Janda</option>
                                <option value="ibu_tunggal">Ibu Tunggal</option>
                            </select>
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor={`child_pendidikan_${index}`}>Tahap Pendidikan (Pilihan)</Label>
                            <select
                                id={`child_pendidikan_${index}`}
                                value={child.pendidikan || ""}
                                onChange={(e) => updateChild(index, 'pendidikan', e.target.value)}
                                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            >
                                <option value="">Pilih tahap pendidikan (tidak wajib)</option>
                                <option value="tiada_pendidikan">Tiada Pendidikan</option>
                                <option value="sekolah_dewasa">Sekolah Dewasa</option>
                                <option value="sekolah_pondok_agama">Sekolah Pondok/Agama</option>
                                <option value="tadika_prasekolah">Tadika/PraSekolah</option>
                                <option value="sekolah_rendah_kebangsaan">Sekolah Rendah Kebangsaan</option>
                                <option value="pt3_pmr_srp">PT3/PMR/SRP</option>
                                <option value="spm">SPM</option>
                                <option value="stpm">STPM</option>
                                <option value="sijil_kemahiran_bertauliah">Sijil Kemahiran Bertauliah</option>
                                <option value="diploma_setaraf">Diploma/Setaraf</option>
                                <option value="ijazah_ke_atas">Ijazah ke Atas</option>
                            </select>
                        </div>

                        {child.tarikh_lahir && Number(calculateAge(child.tarikh_lahir)) >= 18 && (
                            <div className="grid gap-2">
                                <Label htmlFor={`child_pekerjaan_${index}`}>Pekerjaan</Label>
                                <select
                                    id={`child_pekerjaan_${index}`}
                                    value={child.pekerjaan || ""}
                                    onChange={(e) => {
                                        const value = e.target.value;
                                        updateChild(index, 'pekerjaan', value);
                                        if (value !== 'lain_lain') {
                                            updateChild(index, 'pekerjaan_lain', '');
                                        }
                                    }}
                                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                >
                                    <option value="">Pilih pekerjaan</option>
                                    <option value="penjawat_awam">Penjawat Awam</option>
                                    <option value="pekerja_swasta">Pekerja Swasta</option>
                                    <option value="petani">Petani</option>
                                    <option value="nelayan">Nelayan</option>
                                    <option value="usahawan">Usahawan</option>
                                    <option value="surirumah_sepenuh_masa">Surirumah Sepenuh Masa</option>
                                    <option value="pesara">Pesara</option>
                                    <option value="penganggur_belia">Penganggur - Belia (Tiada Kemahiran)</option>
                                    <option value="penganggur_graduan">Penganggur - Graduan</option>
                                    <option value="lain_lain">Lain-lain (Nyatakan)</option>
                                </select>
                                {child.pekerjaan === 'lain_lain' && (
                                    <Input
                                        id={`child_pekerjaan_lain_${index}`}
                                        value={child.pekerjaan_lain}
                                        onChange={e => updateChild(index, 'pekerjaan_lain', e.target.value)}
                                        placeholder="Nyatakan pekerjaan lain"
                                        className="mt-2"
                                    />
                                )}
                            </div>
                        )}

                        <div className="grid gap-2">
                            <Label htmlFor={`child_agama_${index}`}>Agama</Label>
                            <select
                                id={`child_agama_${index}`}
                                value={child.agama || ""}
                                onChange={(e) => {
                                    const value = e.target.value;
                                    updateChild(index, 'agama', value);
                                    if (value !== 'lain-lain') {
                                        updateChild(index, 'agama_lain', '');
                                    }
                                }}
                                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            >
                                <option value="">Pilih agama</option>
                                <option value="islam">Islam</option>
                                <option value="kristian">Kristian</option>
                                <option value="buddha">Buddha</option>
                                <option value="hindu">Hindu</option>
                                <option value="lain-lain">Lain-lain</option>
                            </select>
                            {child.agama === 'lain-lain' && (
                                <Input
                                    id={`child_agama_lain_${index}`}
                                    value={child.agama_lain}
                                    onChange={e => updateChild(index, 'agama_lain', e.target.value)}
                                    placeholder="Nyatakan agama lain"
                                    className="mt-2"
                                />
                            )}
                        </div>
                    </div>

                    <div className="flex items-center space-x-2">
                        <Checkbox
                            id={`child_status_oku_${index}`}
                            checked={child.status_oku}
                            onCheckedChange={(checked) => updateChild(index, 'status_oku', Boolean(checked))}
                        />
                        <Label htmlFor={`child_status_oku_${index}`}>Status OKU</Label>
                    </div>

                    {/* University Study Toggle */}
                    <div className="flex items-center space-x-2">
                        <Checkbox
                            id={`child_sedang_belajar_ipt_${index}`}
                            checked={child.sedang_belajar_ipt}
                            onCheckedChange={(checked) => {
                                updateChild(index, 'sedang_belajar_ipt', Boolean(checked));
                                if (!checked) {
                                    updateChild(index, 'tajaan', '');
                                }
                            }}
                        />
                        <Label htmlFor={`child_sedang_belajar_ipt_${index}`}>Sedang belajar di Institut Pengajian Tinggi (IPT)</Label>
                    </div>

                    {/* IPT Fields - Show only if studying at university */}
                    {child.sedang_belajar_ipt && (
                        <div className="mt-4 p-4 border rounded-lg">
                            <h5 className="font-medium mb-3">Maklumat Institut Pengajian Tinggi</h5>
                            <div className="grid gap-4 md:grid-cols-1">
                                <div className="grid gap-2">
                                    <Label htmlFor={`child_tajaan_${index}`}>Jenis Tajaan</Label>
                                    <select
                                        id={`child_tajaan_${index}`}
                                        value={child.tajaan || ""}
                                        onChange={(e) => updateChild(index, 'tajaan', e.target.value)}
                                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    >
                                        <option value="">Pilih jenis tajaan</option>
                                        <option value="swasta">Swasta</option>
                                        <option value="kerajaan">Kerajaan</option>
                                        <option value="sendiri">Sendiri</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            ))}
        </div>
    );
}
