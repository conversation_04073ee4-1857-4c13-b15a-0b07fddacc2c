<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Anak extends Model
{
    protected $table = 'anak';
    protected $fillable = ['father_id', 'nama', 'jantina', 'tarikh_lahir', 'no_hp', 'status', 'pendidikan', 'pekerjaan', 'pekerjaan_lain', 'agama', 'agama_lain', 'bangsa', 'status_oku', 'sedang_belajar_ipt'];

    public function keluarga()
    {
        return $this->belongsTo(Keluarga::class, 'father_id');
    }

    public function ipt()
    {
        return $this->hasOne(Ipt::class, 'anak_id');
    }
}
