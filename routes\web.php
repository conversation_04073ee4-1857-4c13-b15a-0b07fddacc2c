<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\KeluargaController;
use App\Http\Controllers\IsteriController;
use App\Http\Controllers\AnakController;
use App\Http\Controllers\IptController;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');


Route::post('/keluarga', [KeluargaController::class, 'store'])->name('keluarga.store');
Route::post('/isteri', [IsteriController::class, 'store'])->name('isteri.store');
Route::post('/anak', [AnakController::class, 'store'])->name('anak.store');
Route::post('/ipt', [IptController::class, 'store'])->name('ipt.store');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
    Route::get('/keluarga', [KeluargaController::class, 'index'])->name('keluarga.index');
    Route::get('/isteri', [IsteriController::class, 'index'])->name('isteri.index');
    Route::get('/anak', [AnakController::class, 'index'])->name('anak.index');
    Route::get('/ipt', [IptController::class, 'index'])->name('ipt.index');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
