import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
    Users, 
    Heart, 
    Baby, 
    GraduationCap, 
    Banknote, 
    BarChart3,
    TrendingUp,
    Globe,
    BookOpen,
    Briefcase,
    Home,
    Shield,
    HandHeart,
    User
} from 'lucide-react';

interface SummaryData {
    demographic: {
        total_population: number;
        total_families: number;
        total_houses: number;
        religion_counts: {
            islam: number;
            kristian: number;
            buddha: number;
            hindu: number;
            lain: number;
        };
        age_categories_male: {
            below_12: number;
            '13_to_20': number;
            '21_to_35': number;
            '36_to_59': number;
            '60_above': number;
        };
        age_categories_female: {
            below_12: number;
            '13_to_20': number;
            '21_to_35': number;
            '36_to_59': number;
            '60_above': number;
        };
    };
    ethnicity: {
        male: Record<string, number>;
        female: Record<string, number>;
    };
    education: {
        tiada_pendidikan: { male: number; female: number };
        sekolah_rendah: { male: number; female: number };
        spm: { male: number; female: number };
        stpm: { male: number; female: number };
        diploma: { male: number; female: number };
        ijazah: { male: number; female: number };
    };
    employment: {
        penjawat_awam: { male: number; female: number };
        pekerja_swasta: { male: number; female: number };
        petani: { male: number; female: number };
        usahawan: { male: number; female: number };
        surirumah: { male: number; female: number };
        penganggur: { male: number; female: number };
    };
    income: {
        top_20: number;
        middle_40: number;
        bottom_40: number;
    };
    special_groups: {
        oku_total: number;
        single_parents: number;
        orphans: number;
    };
    generated_at: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Ringkasan',
        href: '/summary',
    },
];

export default function Summary() {
    const [summaryData, setSummaryData] = useState<SummaryData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        fetchSummaryData();
    }, []);

    const fetchSummaryData = async () => {
        try {
            const response = await fetch('/summary-data');
            if (!response.ok) {
                throw new Error('Failed to fetch summary data');
            }
            const data = await response.json();
            setSummaryData(data);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'An error occurred');
        } finally {
            setLoading(false);
        }
    };



    if (loading) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Ringkasan Data" />
                <div className="flex items-center justify-center min-h-[400px]">
                    <div className="text-center">
                        <div className="w-8 h-8 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin mx-auto mb-4"></div>
                        <p className="text-gray-600">Memuat data ringkasan...</p>
                    </div>
                </div>
            </AppLayout>
        );
    }

    if (error) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Ringkasan Data" />
                <div className="flex items-center justify-center min-h-[400px]">
                    <div className="text-center">
                        <p className="text-red-600 mb-2">Error: {error}</p>
                        <button 
                            onClick={fetchSummaryData}
                            className="px-4 py-2 border border-gray-300 rounded text-sm hover:bg-gray-50"
                        >
                            Cuba Lagi
                        </button>
                    </div>
                </div>
            </AppLayout>
        );
    }

    if (!summaryData) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Ringkasan Data" />
                <div className="text-center py-8">
                    <p className="text-gray-600">Tiada data tersedia</p>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Ringkasan Data Demografi" />
            
            <div className="space-y-8 p-6">
                {/* Header */}
                <div className="border-b pb-6">
                    <div className="flex items-center gap-3 mb-2">
                        <BarChart3 className="h-6 w-6 text-gray-600" />
                        <h1 className="text-2xl font-semibold text-gray-900">Ringkasan Data Demografi</h1>
                    </div>
                    <p className="text-gray-600">
                        Data dikemaskini pada: {new Date(summaryData.generated_at).toLocaleString('ms-MY', {
                            timeZone: 'Asia/Kuala_Lumpur',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                        })}
                    </p>
                </div>

                {/* B. MAKLUMAT DEMOGRAFI */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Users className="h-5 w-5" />
                            B. MAKLUMAT DEMOGRAFI
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        {/* B1: BILANGAN PENDUDUK & UMUR */}
                        <div>
                            <h3 className="font-semibold mb-4 text-lg flex items-center gap-2">
                                <User className="h-4 w-4" />
                                B1: BILANGAN PENDUDUK & UMUR
                            </h3>
                            
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                <Card className="border">
                                    <CardContent className="p-4">
                                        <div className="flex items-center gap-2">
                                            <Users className="h-5 w-5 text-gray-600" />
                                            <div>
                                                <p className="text-sm text-gray-600">Jumlah Penduduk</p>
                                                <p className="text-2xl font-semibold text-gray-900">
                                                    {summaryData.demographic.total_population}
                                                </p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                                
                                <Card className="border">
                                    <CardContent className="p-4">
                                        <div className="flex items-center gap-2">
                                            <Heart className="h-5 w-5 text-gray-600" />
                                            <div>
                                                <p className="text-sm text-gray-600">Jumlah Keluarga</p>
                                                <p className="text-2xl font-semibold text-gray-900">
                                                    {summaryData.demographic.total_families}
                                                </p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                                
                                <Card className="border">
                                    <CardContent className="p-4">
                                        <div className="flex items-center gap-2">
                                            <Home className="h-5 w-5 text-gray-600" />
                                            <div>
                                                <p className="text-sm text-gray-600">Bilangan Rumah</p>
                                                <p className="text-2xl font-semibold text-gray-900">
                                                    {summaryData.demographic.total_houses}
                                                </p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Agama */}
                            <div className="mb-6">
                                <h4 className="font-medium mb-3">Jumlah Penganut Beragama:</h4>
                                <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
                                    <div className="border p-3 rounded">
                                        <p className="text-sm text-gray-600">Islam</p>
                                        <p className="font-semibold text-gray-900">{summaryData.demographic.religion_counts.islam}</p>
                                    </div>
                                    <div className="border p-3 rounded">
                                        <p className="text-sm text-gray-600">Kristian</p>
                                        <p className="font-semibold text-gray-900">{summaryData.demographic.religion_counts.kristian}</p>
                                    </div>
                                    <div className="border p-3 rounded">
                                        <p className="text-sm text-gray-600">Buddha</p>
                                        <p className="font-semibold text-gray-900">{summaryData.demographic.religion_counts.buddha}</p>
                                    </div>
                                    <div className="border p-3 rounded">
                                        <p className="text-sm text-gray-600">Hindu</p>
                                        <p className="font-semibold text-gray-900">{summaryData.demographic.religion_counts.hindu}</p>
                                    </div>
                                    <div className="border p-3 rounded">
                                        <p className="text-sm text-gray-600">Lain-lain</p>
                                        <p className="font-semibold text-gray-900">{summaryData.demographic.religion_counts.lain}</p>
                                    </div>
                                </div>
                            </div>

                            {/* Kategori Umur */}
                            <div>
                                <h4 className="font-medium mb-3">Kategori Umur:</h4>
                                <div className="rounded-md border">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Kategori Umur</TableHead>
                                                <TableHead className="text-center">Lelaki</TableHead>
                                                <TableHead className="text-center">Perempuan</TableHead>
                                                <TableHead className="text-center">Jumlah</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            <TableRow>
                                                <TableCell>12 tahun dan ke bawah</TableCell>
                                                <TableCell className="text-center">
                                                    {summaryData.demographic.age_categories_male.below_12}
                                                </TableCell>
                                                <TableCell className="text-center">
                                                    {summaryData.demographic.age_categories_female.below_12}
                                                </TableCell>
                                                <TableCell className="text-center font-medium">
                                                    {summaryData.demographic.age_categories_male.below_12 + 
                                                     summaryData.demographic.age_categories_female.below_12}
                                                </TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell>13 hingga 20 tahun</TableCell>
                                                <TableCell className="text-center">
                                                    {summaryData.demographic.age_categories_male['13_to_20']}
                                                </TableCell>
                                                <TableCell className="text-center">
                                                    {summaryData.demographic.age_categories_female['13_to_20']}
                                                </TableCell>
                                                <TableCell className="text-center font-medium">
                                                    {summaryData.demographic.age_categories_male['13_to_20'] + 
                                                     summaryData.demographic.age_categories_female['13_to_20']}
                                                </TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell>21 hingga 35 tahun</TableCell>
                                                <TableCell className="text-center">
                                                    {summaryData.demographic.age_categories_male['21_to_35']}
                                                </TableCell>
                                                <TableCell className="text-center">
                                                    {summaryData.demographic.age_categories_female['21_to_35']}
                                                </TableCell>
                                                <TableCell className="text-center font-medium">
                                                    {summaryData.demographic.age_categories_male['21_to_35'] + 
                                                     summaryData.demographic.age_categories_female['21_to_35']}
                                                </TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell>36 hingga 59 tahun</TableCell>
                                                <TableCell className="text-center">
                                                    {summaryData.demographic.age_categories_male['36_to_59']}
                                                </TableCell>
                                                <TableCell className="text-center">
                                                    {summaryData.demographic.age_categories_female['36_to_59']}
                                                </TableCell>
                                                <TableCell className="text-center font-medium">
                                                    {summaryData.demographic.age_categories_male['36_to_59'] + 
                                                     summaryData.demographic.age_categories_female['36_to_59']}
                                                </TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell>60 tahun dan ke atas</TableCell>
                                                <TableCell className="text-center">
                                                    {summaryData.demographic.age_categories_male['60_above']}
                                                </TableCell>
                                                <TableCell className="text-center">
                                                    {summaryData.demographic.age_categories_female['60_above']}
                                                </TableCell>
                                                <TableCell className="text-center font-medium">
                                                    {summaryData.demographic.age_categories_male['60_above'] + 
                                                     summaryData.demographic.age_categories_female['60_above']}
                                                </TableCell>
                                            </TableRow>
                                        </TableBody>
                                    </Table>
                                </div>
                            </div>
                        </div>

                        <Separator />

                        {/* B2: BANGSA */}
                        <div>
                            <h3 className="font-semibold mb-4 text-lg flex items-center gap-2">
                                <Globe className="h-4 w-4" />
                                B2: BANGSA
                            </h3>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <h4 className="font-medium mb-3">Lelaki</h4>
                                    <div className="space-y-2">
                                        {Object.entries(summaryData.ethnicity.male).map(([bangsa, count]) => (
                                            <div key={bangsa} className="flex justify-between items-center border p-2 rounded">
                                                <span className="text-sm text-gray-700">{bangsa}</span>
                                                <span className="text-sm font-medium text-gray-900">{count}</span>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                                
                                <div>
                                    <h4 className="font-medium mb-3">Perempuan</h4>
                                    <div className="space-y-2">
                                        {Object.entries(summaryData.ethnicity.female).map(([bangsa, count]) => (
                                            <div key={bangsa} className="flex justify-between items-center border p-2 rounded">
                                                <span className="text-sm text-gray-700">{bangsa}</span>
                                                <span className="text-sm font-medium text-gray-900">{count}</span>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* C. MAKLUMAT TAHAP PENDIDIKAN */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <BookOpen className="h-5 w-5" />
                            C. MAKLUMAT TAHAP PENDIDIKAN
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div>
                            <h3 className="font-semibold mb-4 text-lg flex items-center gap-2">
                                <GraduationCap className="h-4 w-4" />
                                C1: TAHAP PENDIDIKAN TERTINGGI
                            </h3>
                            
                            <div className="rounded-md border">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Perkara</TableHead>
                                            <TableHead className="text-center">Lelaki</TableHead>
                                            <TableHead className="text-center">Perempuan</TableHead>
                                            <TableHead className="text-center">Jumlah</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        <TableRow>
                                            <TableCell>Tiada Pendidikan</TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.education.tiada_pendidikan.male}
                                            </TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.education.tiada_pendidikan.female}
                                            </TableCell>
                                            <TableCell className="text-center font-medium">
                                                {summaryData.education.tiada_pendidikan.male + summaryData.education.tiada_pendidikan.female}
                                            </TableCell>
                                        </TableRow>
                                        <TableRow>
                                            <TableCell>Sekolah Rendah</TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.education.sekolah_rendah.male}
                                            </TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.education.sekolah_rendah.female}
                                            </TableCell>
                                            <TableCell className="text-center font-medium">
                                                {summaryData.education.sekolah_rendah.male + summaryData.education.sekolah_rendah.female}
                                            </TableCell>
                                        </TableRow>
                                        <TableRow>
                                            <TableCell>SPM</TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.education.spm.male}
                                            </TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.education.spm.female}
                                            </TableCell>
                                            <TableCell className="text-center font-medium">
                                                {summaryData.education.spm.male + summaryData.education.spm.female}
                                            </TableCell>
                                        </TableRow>
                                        <TableRow>
                                            <TableCell>STPM</TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.education.stpm.male}
                                            </TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.education.stpm.female}
                                            </TableCell>
                                            <TableCell className="text-center font-medium">
                                                {summaryData.education.stpm.male + summaryData.education.stpm.female}
                                            </TableCell>
                                        </TableRow>
                                        <TableRow>
                                            <TableCell>Diploma/Setaraf</TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.education.diploma.male}
                                            </TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.education.diploma.female}
                                            </TableCell>
                                            <TableCell className="text-center font-medium">
                                                {summaryData.education.diploma.male + summaryData.education.diploma.female}
                                            </TableCell>
                                        </TableRow>
                                        <TableRow>
                                            <TableCell>Ijazah ke Atas</TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.education.ijazah.male}
                                            </TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.education.ijazah.female}
                                            </TableCell>
                                            <TableCell className="text-center font-medium">
                                                {summaryData.education.ijazah.male + summaryData.education.ijazah.female}
                                            </TableCell>
                                        </TableRow>
                                    </TableBody>
                                </Table>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* D. MAKLUMAT PEKERJAAN DAN PENDAPATAN */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Briefcase className="h-5 w-5" />
                            D. MAKLUMAT PEKERJAAN DAN PENDAPATAN
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        {/* D1: PEKERJAAN */}
                        <div>
                            <h3 className="font-semibold mb-4 text-lg">D1: PEKERJAAN</h3>
                            
                            <div className="rounded-md border">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Perkara</TableHead>
                                            <TableHead className="text-center">Lelaki</TableHead>
                                            <TableHead className="text-center">Perempuan</TableHead>
                                            <TableHead className="text-center">Jumlah</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        <TableRow>
                                            <TableCell>Penjawat Awam</TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.employment.penjawat_awam.male}
                                            </TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.employment.penjawat_awam.female}
                                            </TableCell>
                                            <TableCell className="text-center font-medium">
                                                {summaryData.employment.penjawat_awam.male + summaryData.employment.penjawat_awam.female}
                                            </TableCell>
                                        </TableRow>
                                        <TableRow>
                                            <TableCell>Pekerja Swasta</TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.employment.pekerja_swasta.male}
                                            </TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.employment.pekerja_swasta.female}
                                            </TableCell>
                                            <TableCell className="text-center font-medium">
                                                {summaryData.employment.pekerja_swasta.male + summaryData.employment.pekerja_swasta.female}
                                            </TableCell>
                                        </TableRow>
                                        <TableRow>
                                            <TableCell>Petani</TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.employment.petani.male}
                                            </TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.employment.petani.female}
                                            </TableCell>
                                            <TableCell className="text-center font-medium">
                                                {summaryData.employment.petani.male + summaryData.employment.petani.female}
                                            </TableCell>
                                        </TableRow>
                                        <TableRow>
                                            <TableCell>Usahawan</TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.employment.usahawan.male}
                                            </TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.employment.usahawan.female}
                                            </TableCell>
                                            <TableCell className="text-center font-medium">
                                                {summaryData.employment.usahawan.male + summaryData.employment.usahawan.female}
                                            </TableCell>
                                        </TableRow>
                                        <TableRow>
                                            <TableCell>Surirumah</TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.employment.surirumah.male}
                                            </TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.employment.surirumah.female}
                                            </TableCell>
                                            <TableCell className="text-center font-medium">
                                                {summaryData.employment.surirumah.male + summaryData.employment.surirumah.female}
                                            </TableCell>
                                        </TableRow>
                                        <TableRow>
                                            <TableCell>Penganggur</TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.employment.penganggur.male}
                                            </TableCell>
                                            <TableCell className="text-center">
                                                {summaryData.employment.penganggur.female}
                                            </TableCell>
                                            <TableCell className="text-center font-medium">
                                                {summaryData.employment.penganggur.male + summaryData.employment.penganggur.female}
                                            </TableCell>
                                        </TableRow>
                                    </TableBody>
                                </Table>
                            </div>
                        </div>

                        <Separator />

                        {/* D2: PURATA PENDAPATAN BULANAN ISI RUMAH */}
                        <div>
                            <h3 className="font-semibold mb-4 text-lg flex items-center gap-2">
                                <TrendingUp className="h-4 w-4" />
                                D2: PURATA PENDAPATAN BULANAN ISI RUMAH
                            </h3>
                            
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <Card className="border">
                                    <CardContent className="p-4">
                                        <div className="flex items-center gap-2">
                                            <Banknote className="h-5 w-5 text-gray-600" />
                                            <div>
                                                <p className="text-sm text-gray-600">Top 20%</p>
                                                <p className="text-xs text-gray-500">RM 8,319 dan ke atas</p>
                                                <p className="text-2xl font-semibold text-gray-900">
                                                    {summaryData.income.top_20}
                                                </p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                                
                                <Card className="border">
                                    <CardContent className="p-4">
                                        <div className="flex items-center gap-2">
                                            <Banknote className="h-5 w-5 text-gray-600" />
                                            <div>
                                                <p className="text-sm text-gray-600">Middle 40%</p>
                                                <p className="text-xs text-gray-500">RM 3,860 - RM 8,319</p>
                                                <p className="text-2xl font-semibold text-gray-900">
                                                    {summaryData.income.middle_40}
                                                </p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                                
                                <Card className="border">
                                    <CardContent className="p-4">
                                        <div className="flex items-center gap-2">
                                            <Banknote className="h-5 w-5 text-gray-600" />
                                            <div>
                                                <p className="text-sm text-gray-600">Bottom 40%</p>
                                                <p className="text-xs text-gray-500">Di bawah RM 3,860</p>
                                                <p className="text-2xl font-semibold text-gray-900">
                                                    {summaryData.income.bottom_40}
                                                </p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* E. MAKLUMAT GOLONGAN KHAS */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <HandHeart className="h-5 w-5" />
                            E. MAKLUMAT GOLONGAN KHAS
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div>
                            <h3 className="font-semibold mb-4 text-lg">E1: MAKLUMAT GOLONGAN KHAS</h3>
                            
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <Card className="border">
                                    <CardContent className="p-4">
                                        <div className="flex items-center gap-2">
                                            <Shield className="h-5 w-5 text-gray-600" />
                                            <div>
                                                <p className="text-sm text-gray-600">Bilangan OKU</p>
                                                <p className="text-2xl font-semibold text-gray-900">
                                                    {summaryData.special_groups.oku_total}
                                                </p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                                
                                <Card className="border">
                                    <CardContent className="p-4">
                                        <div className="flex items-center gap-2">
                                            <Heart className="h-5 w-5 text-gray-600" />
                                            <div>
                                                <p className="text-sm text-gray-600">Ibu/Bapa Tunggal</p>
                                                <p className="text-2xl font-semibold text-gray-900">
                                                    {summaryData.special_groups.single_parents}
                                                </p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                                
                                <Card className="border">
                                    <CardContent className="p-4">
                                        <div className="flex items-center gap-2">
                                            <Baby className="h-5 w-5 text-gray-600" />
                                            <div>
                                                <p className="text-sm text-gray-600">Anak Yatim</p>
                                                <p className="text-2xl font-semibold text-gray-900">
                                                    {summaryData.special_groups.orphans}
                                                </p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
