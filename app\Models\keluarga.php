<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class keluarga extends Model
{
    protected $table = 'keluarga';
    protected $fillable = ['lot', 'nama', 'tarikh_lahir', 'no_hp', 'status', 'pendidikan', 'peker<PERSON>an', 'agama', 'bangsa', 'status_oku', 'e-kasih', 'pprt', 'pendapatan'];

    public function isteri()
    {
        return $this->hasOne(Isteri::class);
    }

    public function anak()
    {
        return $this->hasMany(Anak::class);
    }
}
