<?php

namespace App\Http\Controllers;

use App\Models\Keluarga;
use App\Models\Isteri;
use App\Models\Anak;
use App\Models\Ipt;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class KeluargaController extends Controller
{
    public function index()
    {
        return response()->json(Keluarga::with(['isteri', 'anak.ipt'])->latest()->get());
    }

    public function show($id)
    {
        try {
            $keluarga = Keluarga::with(['isteri', 'anak.ipt'])->findOrFail($id);
            return response()->json($keluarga);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Family not found'], 404);
        }
    }

    public function edit($id)
    {
        try {
            $keluarga = Keluarga::with(['isteri', 'anak.ipt'])->findOrFail($id);
            
            // Transform data to match the form structure
            $formData = [
                // Family head data
                'lot' => (string) ($keluarga->lot ?? ''),
                'bil_rumah' => (string) ($keluarga->bil_rumah ?? ''),
                'nama' => $keluarga->nama ?? '',
                'tarikh_lahir' => $keluarga->tarikh_lahir ?? '',
                'no_hp' => $keluarga->no_hp ?? '',
                'status' => $keluarga->status ?? '',
                'pendidikan' => $keluarga->pendidikan ?? '',
                'pekerjaan' => $keluarga->pekerjaan ?? '',
                'pekerjaan_lain' => $keluarga->pekerjaan_lain ?? '',
                'agama' => $keluarga->agama ?? '',
                'agama_lain' => $keluarga->agama_lain ?? '',
                'bangsa' => $keluarga->bangsa ?? '',
                'status_oku' => (bool) ($keluarga->status_oku ?? false),
                'e-kasih' => (bool) ($keluarga->{'e-kasih'} ?? false),
                'pprt' => (bool) ($keluarga->pprt ?? false),
                'pendapatan' => (string) ($keluarga->pendapatan ?? ''),
                
                // Wife data
                'wife' => [
                    'nama' => $keluarga->isteri->nama ?? '',
                    'tarikh_lahir' => $keluarga->isteri->tarikh_lahir ?? '',
                    'no_hp' => $keluarga->isteri->no_hp ?? '',
                    'status' => $keluarga->isteri->status ?? '',
                    'pendidikan' => $keluarga->isteri->pendidikan ?? '',
                    'pekerjaan' => $keluarga->isteri->pekerjaan ?? '',
                    'pekerjaan_lain' => $keluarga->isteri->pekerjaan_lain ?? '',
                    'agama' => $keluarga->isteri->agama ?? '',
                    'agama_lain' => $keluarga->isteri->agama_lain ?? '',
                    'bangsa' => $keluarga->isteri->bangsa ?? '',
                    'status_oku' => (bool) ($keluarga->isteri->status_oku ?? false),
                ],
                
                // Children data
                'children' => $keluarga->anak->map(function ($child) {
                    return [
                        'id' => (string) $child->id,
                        'nama' => $child->nama ?? '',
                        'jantina' => $child->jantina ?? 'lelaki',
                        'tarikh_lahir' => $child->tarikh_lahir ?? '',
                        'no_hp' => $child->no_hp ?? '',
                        'status' => $child->status ?? '',
                        'pendidikan' => $child->pendidikan ?? '',
                        'pekerjaan' => $child->pekerjaan ?? '',
                        'pekerjaan_lain' => $child->pekerjaan_lain ?? '',
                        'agama' => $child->agama ?? '',
                        'agama_lain' => $child->agama_lain ?? '',
                        'status_oku' => (bool) ($child->status_oku ?? false),
                        'sedang_belajar_ipt' => (bool) ($child->sedang_belajar_ipt ?? false),
                        'tajaan' => $child->ipt->tajaan ?? '',
                    ];
                })->toArray()
            ];
            
            return inertia('EditFamily', [
                'formData' => $formData,
                'familyId' => $id,
                'isEditing' => true
            ]);
        } catch (\Exception $e) {
            return redirect('/kemaskini')->withErrors(['error' => 'Family not found']);
        }
    }

    public function update(Request $request, $id)
    {
        try {
            // Start with basic input processing
            $input = $request->all();
            
            // Handle null/empty values for numeric and optional fields
            $this->processInputData($input);
            
            $request->merge($input);

            // Validate all data at once
            $request->validate([
                // Keluarga validation
                'lot' => 'nullable|integer|min:1',
                'bil_rumah' => 'nullable|integer|min:1',
                'nama' => 'required|string|max:255',
                'tarikh_lahir' => 'required|date',
                'no_hp' => 'nullable|string|max:15',
                'status' => 'nullable|string|max:255',
                'pendidikan' => 'nullable|string|max:255',
                'pekerjaan' => 'nullable|string|max:255',
                'pekerjaan_lain' => 'nullable|string|max:255',
                'agama' => 'nullable|string|max:255',
                'agama_lain' => 'nullable|string|max:255',
                'bangsa' => 'nullable|string|max:255',
                'status_oku' => 'nullable|boolean',
                'e-kasih' => 'nullable|boolean',
                'pprt' => 'nullable|boolean',
                'pendapatan' => 'nullable|numeric|min:0',
                
                // Wife validation
                'wife.nama' => 'nullable|string|max:255',
                'wife.tarikh_lahir' => 'nullable|date',
                'wife.no_hp' => 'nullable|string|max:15',
                'wife.status' => 'nullable|string|max:255',
                'wife.pendidikan' => 'nullable|string|max:255',
                'wife.pekerjaan' => 'nullable|string|max:255',
                'wife.pekerjaan_lain' => 'nullable|string|max:255',
                'wife.agama' => 'nullable|string|max:255',
                'wife.agama_lain' => 'nullable|string|max:255',
                'wife.bangsa' => 'nullable|string|max:255',
                'wife.status_oku' => 'nullable|boolean',
                
                // Children validation
                'children' => 'required|array|min:1',
                'children.*.nama' => 'required|string|max:255',
                'children.*.jantina' => 'required|in:lelaki,perempuan',
                'children.*.tarikh_lahir' => 'required|date',
                'children.*.no_hp' => 'nullable|string|max:15',
                'children.*.status' => 'nullable|string|max:255',
                'children.*.pendidikan' => 'nullable|string|max:255',
                'children.*.pekerjaan' => 'nullable|string|max:255',
                'children.*.pekerjaan_lain' => 'nullable|string|max:255',
                'children.*.agama' => 'required|string|max:255',
                'children.*.agama_lain' => 'nullable|string|max:255',
                'children.*.status_oku' => 'nullable|boolean',
                'children.*.sedang_belajar_ipt' => 'nullable|boolean',
                'children.*.tajaan' => 'nullable|in:swasta,kerajaan,sendiri',
            ]);

            DB::beginTransaction();
            
            // Debug: Log the incoming data
            Log::info('Form update data:', $request->all());

            // Find the existing family record
            $keluarga = Keluarga::findOrFail($id);

            // Update Keluarga (Family Head) record
            $keluargaData = $this->prepareKeluargaData($request);
            $keluarga->update($keluargaData);

            // Handle Wife record
            $wifeData = $request->input('wife');
            if (!empty($wifeData['nama'])) {
                $wifeData = $this->prepareIsteriData($wifeData, $keluarga->id);
                
                // Update or create wife record
                $existingWife = Isteri::where('father_id', $keluarga->id)->first();
                if ($existingWife) {
                    $existingWife->update($wifeData);
                } else {
                    Isteri::create($wifeData);
                }
            } else {
                // Delete wife record if no name provided
                Isteri::where('father_id', $keluarga->id)->delete();
            }

            // Handle Children records
            $existingChildren = Anak::where('father_id', $keluarga->id)->get();
            $submittedChildrenIds = [];

            foreach ($request->input('children') as $childData) {
                $childData = $this->prepareAnakData($childData, $keluarga->id, $keluarga->bangsa);
                
                // Check if this is an existing child (has an ID)
                if (isset($childData['id']) && $childData['id']) {
                    $child = Anak::find($childData['id']);
                    if ($child && $child->father_id == $keluarga->id) {
                        // Update existing child
                        $child->update($childData);
                        $submittedChildrenIds[] = $child->id;
                        
                        // Handle IPT record
                        $this->handleIptRecord($child, $childData);
                    }
                } else {
                    // Create new child
                    $child = Anak::create($childData);
                    $submittedChildrenIds[] = $child->id;
                    
                    // Handle IPT record
                    $this->handleIptRecord($child, $childData);
                }
            }

            // Delete children that were not submitted (removed from form)
            Anak::where('father_id', $keluarga->id)
                ->whereNotIn('id', $submittedChildrenIds)
                ->delete();

            DB::commit();

            return redirect()->back()->with('success', 'Data keluarga berjaya dikemaskini!');

        } catch (\Illuminate\Validation\ValidationException $e) {
            DB::rollback();
            
            return redirect()->back()->withErrors($e->errors())->withInput();
            
        } catch (\Exception $e) {
            DB::rollback();
            
            Log::error('Failed to update family data: ' . $e->getMessage(), [
                'exception' => $e,
                'request_data' => $request->all()
            ]);
            
            return redirect()->back()->withErrors([
                'submission' => 'Gagal mengemaskini data: ' . $e->getMessage()
            ])->withInput();
        }
    }

    public function store(Request $request)
    {
        try {
            // Start with basic input processing
            $input = $request->all();
            
            // Handle null/empty values for numeric and optional fields
            $this->processInputData($input);
            
            $request->merge($input);

            // Validate all data at once
            $request->validate([
                // Keluarga validation
                'lot' => 'nullable|integer|min:1',
                'bil_rumah' => 'nullable|integer|min:1',
                'nama' => 'required|string|max:255',
                'tarikh_lahir' => 'required|date',
                'no_hp' => 'nullable|string|max:15',
                'status' => 'nullable|string|max:255',
                'pendidikan' => 'nullable|string|max:255',
                'pekerjaan' => 'nullable|string|max:255',
                'pekerjaan_lain' => 'nullable|string|max:255',
                'agama' => 'nullable|string|max:255',
                'agama_lain' => 'nullable|string|max:255',
                'bangsa' => 'nullable|string|max:255',
                'status_oku' => 'nullable|boolean',
                'e-kasih' => 'nullable|boolean',
                'pprt' => 'nullable|boolean',
                'pendapatan' => 'nullable|numeric|min:0',
                
                // Wife validation
                'wife.nama' => 'nullable|string|max:255',
                'wife.tarikh_lahir' => 'nullable|date',
                'wife.no_hp' => 'nullable|string|max:15',
                'wife.status' => 'nullable|string|max:255',
                'wife.pendidikan' => 'nullable|string|max:255',
                'wife.pekerjaan' => 'nullable|string|max:255',
                'wife.pekerjaan_lain' => 'nullable|string|max:255',
                'wife.agama' => 'nullable|string|max:255',
                'wife.agama_lain' => 'nullable|string|max:255',
                'wife.bangsa' => 'nullable|string|max:255',
                'wife.status_oku' => 'nullable|boolean',
                
                // Children validation
                'children' => 'required|array|min:1',
                'children.*.nama' => 'required|string|max:255',
                'children.*.jantina' => 'required|in:lelaki,perempuan',
                'children.*.tarikh_lahir' => 'required|date',
                'children.*.no_hp' => 'nullable|string|max:15',
                'children.*.status' => 'nullable|string|max:255',
                'children.*.pendidikan' => 'nullable|string|max:255',
                'children.*.pekerjaan' => 'nullable|string|max:255',
                'children.*.pekerjaan_lain' => 'nullable|string|max:255',
                'children.*.agama' => 'required|string|max:255',
                'children.*.agama_lain' => 'nullable|string|max:255',
                'children.*.status_oku' => 'nullable|boolean',
                'children.*.sedang_belajar_ipt' => 'nullable|boolean',
                'children.*.tajaan' => 'nullable|in:swasta,kerajaan,sendiri',
            ]);

            DB::beginTransaction();
            
            // Debug: Log the incoming data
            Log::info('Form submission data:', $request->all());

            // Create Keluarga (Family Head) record with default values
            $keluargaData = $this->prepareKeluargaData($request);
            $keluarga = Keluarga::create($keluargaData);

            // Create Isteri (Wife) record if wife data exists
            $wifeData = $request->input('wife');
            if (!empty($wifeData['nama'])) {
                $wifeData = $this->prepareIsteriData($wifeData, $keluarga->id);
                Isteri::create($wifeData);
            }

            // Create Anak (Children) records
            foreach ($request->input('children') as $childData) {
                $childData = $this->prepareAnakData($childData, $keluarga->id, $keluarga->bangsa);
                $anak = Anak::create($childData);

                // Create IPT record if child is studying at university
                if (($childData['sedang_belajar_ipt'] ?? false) && !empty($childData['tajaan'])) {
                    Ipt::create([
                        'anak_id' => $anak->id,
                        'tajaan' => $childData['tajaan']
                    ]);
                }
            }

            DB::commit();

            return redirect()->back()->with('success', 'Data keluarga berjaya disimpan!');

        } catch (\Illuminate\Validation\ValidationException $e) {
            DB::rollback();
            
            return redirect()->back()->withErrors($e->errors())->withInput();
            
        } catch (\Exception $e) {
            DB::rollback();
            
            Log::error('Failed to save family data: ' . $e->getMessage(), [
                'exception' => $e,
                'request_data' => $request->all()
            ]);
            
            return redirect()->back()->withErrors([
                'submission' => 'Gagal menyimpan data: ' . $e->getMessage()
            ])->withInput();
        }
    }

    /**
     * Process input data to handle null/empty values
     */
    private function processInputData(&$input)
    {
        // Convert empty strings to null for numeric fields
        $numericFields = ['lot', 'bil_rumah', 'pendapatan'];
        foreach ($numericFields as $field) {
            if (isset($input[$field]) && $input[$field] === '') {
                $input[$field] = null;
            }
        }

        // Process wife data
        if (isset($input['wife']) && is_array($input['wife'])) {
            foreach ($input['wife'] as $key => $value) {
                if ($value === '') {
                    $input['wife'][$key] = null;
                }
            }
        }

        // Process children data
        if (isset($input['children']) && is_array($input['children'])) {
            foreach ($input['children'] as $index => $child) {
                foreach ($child as $key => $value) {
                    if ($value === '') {
                        $input['children'][$index][$key] = null;
                    }
                }
            }
        }
    }

    /**
     * Prepare Keluarga data with default values for non-nullable fields
     */
    private function prepareKeluargaData(Request $request)
    {
        $data = $request->only([
            'lot', 'bil_rumah', 'nama', 'tarikh_lahir', 'no_hp', 'status', 
            'pendidikan', 'pekerjaan', 'pekerjaan_lain', 'agama', 'agama_lain', 
            'bangsa', 'status_oku', 'e-kasih', 'pprt', 'pendapatan'
        ]);

        // Set default values for non-nullable fields
        return array_merge([
            'lot' => 0,
            'bil_rumah' => 0,
            'no_hp' => '',
            'status' => '',
            'pendidikan' => '',
            'pekerjaan' => '',
            'agama' => '',
            'bangsa' => '',
            'status_oku' => false,
            'e-kasih' => false,
            'pprt' => false,
            'pendapatan' => 0.00,
        ], array_filter($data, function($value) {
            return $value !== null;
        }));
    }

    /**
     * Prepare Isteri data with default values for non-nullable fields
     */
    private function prepareIsteriData($wifeData, $fatherId)
    {
        $wifeData['father_id'] = $fatherId;
        
        // Set default values for non-nullable fields
        return array_merge([
            'nama' => '',
            'tarikh_lahir' => now()->format('Y-m-d'),
            'no_hp' => '',
            'status' => '',
            'pendidikan' => '',
            'pekerjaan' => '',
            'agama' => '',
            'bangsa' => '',
            'status_oku' => false,
        ], array_filter($wifeData, function($value) {
            return $value !== null;
        }));
    }

    /**
     * Prepare Anak data with default values for non-nullable fields
     */
    private function prepareAnakData($childData, $fatherId, $fatherBangsa)
    {
        // Remove the temporary id field if it exists
        unset($childData['id']);
        
        $childData['father_id'] = $fatherId;
        // Children inherit father's bangsa
        $childData['bangsa'] = $fatherBangsa ?? '';
        
        // Set default values for non-nullable fields
        return array_merge([
            'nama' => '',
            'jantina' => 'lelaki',
            'tarikh_lahir' => now()->format('Y-m-d'),
            'status' => '',
            'pendidikan' => '',
            'agama' => '',
            'status_oku' => false,
            'sedang_belajar_ipt' => false,
        ], array_filter($childData, function($value) {
            return $value !== null;
        }));
    }

    private function handleIptRecord($child, $childData)
    {
        // Handle IPT record
        if (($childData['sedang_belajar_ipt'] ?? false) && !empty($childData['tajaan'])) {
            $existingIpt = Ipt::where('anak_id', $child->id)->first();
            if ($existingIpt) {
                $existingIpt->update([
                    'tajaan' => $childData['tajaan']
                ]);
            } else {
                Ipt::create([
                    'anak_id' => $child->id,
                    'tajaan' => $childData['tajaan']
                ]);
            }
        } else {
            // Delete IPT record if child is not studying at university
            Ipt::where('anak_id', $child->id)->delete();
        }
    }

    public function searchByPhone(Request $request)
    {
        $request->validate([
            'no_hp' => 'required|string|max:15',
        ]);

        try {
            // Search for family by phone number (could be family head or wife)
            $keluarga = Keluarga::where('no_hp', $request->no_hp)
                ->with(['isteri', 'anak.ipt'])
                ->first();

            // If not found in family head, search in wife records
            if (!$keluarga) {
                $isteri = Isteri::where('no_hp', $request->no_hp)->first();
                if ($isteri) {
                    $keluarga = Keluarga::where('id', $isteri->father_id)
                        ->with(['isteri', 'anak.ipt'])
                        ->first();
                }
            }

            if ($keluarga) {
                return response()->json([
                    'success' => true,
                    'data' => $keluarga
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Data tidak dijumpai'
                ], 404);
            }
        } catch (\Exception $e) {
            Log::error('Error searching family by phone: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Ralat berlaku semasa mencari data'
            ], 500);
        }
    }
}
