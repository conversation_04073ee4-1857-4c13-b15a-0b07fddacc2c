<?php

namespace App\Http\Controllers;

use App\Models\Keluarga;
use Illuminate\Http\Request;    

class KeluargaController extends Controller
{
    public function index()
    {
        return response()->json(Keluarga::latest()->get());
    }

    public function store(Request $request)
    {
        $request->validate([
            'lot' => 'required|integer|max:10',
            'bil_rumah' => 'required|integer|max:10',
            'nama' => 'required|string|max:255',
            'tarikh_lahir' => 'required|date',
            'no_hp' => 'required|string|max:15',
            'status' => 'required|string|max:255',
            'pendidikan' => 'required|string|max:255',
            'pekerjaan' => 'required|string|max:255',
            'agama' => 'required|string|max:255',
            'bangsa' => 'required|string|max:255',
            'status_oku' => 'required|boolean',
            'e-kasih' => 'required|boolean',
            'pprt' => 'required|boolean',
            'pendapatan' => 'required|numeric|min:0',
        ]);

        $keluarga = Keluarga::create($request->all());
        return response()->json($keluarga, 201);
    }
}
