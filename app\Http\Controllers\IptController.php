<?php

namespace App\Http\Controllers;

use App\Models\Ipt;
use Illuminate\Http\Request;

class IptController extends Controller
{
    public function index()
    {
        return response()->json(Ipt::latest()->get());
    }

    public function store(Request $request)
    {
        $request->validate([
            'anak_id' => 'required|exists:anak,id',
            'tajaan' => 'required|string|max:255',
        ]);

        $ipt = Ipt::create($request->all());

        return response()->json($ipt, 201);
    }
}
