import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { DatePicker } from '@/components/ui/date-picker';

interface IsteriFormProps {
    data: {
        nama: string;
        tarikh_lahir: string;
        no_hp: string;
        status: string;
        pendidikan: string;
        pekerjaan: string;
        pekerjaan_lain: string;
        agama: string;
        agama_lain: string;
        bangsa: string;
        status_oku: boolean;
    };
    setData: (key: string, value: any) => void;
    calculateAge: (date: string) => string | number;
    fullData: any;
}

export default function IsteriForm({ data, setData, calculateAge, fullData }: IsteriFormProps) {
    // Use the same pattern as keluarga form - direct setData calls
    return (
        <div className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
                <div className="grid gap-2">
                    <Label htmlFor="wife_nama">Nama <PERSON>i</Label>
                    <Input
                        id="wife_nama"
                        value={data.nama}
                        onChange={e => setData('wife.nama', e.target.value)}
                        placeholder="Contoh: Siti Binti Abdullah"
                    />
                </div>

                <div className="grid gap-2">
                    <Label htmlFor="wife_tarikh_lahir">Tarikh Lahir</Label>
                    <DatePicker
                        id="wife_tarikh_lahir"
                        value={data.tarikh_lahir}
                        onChange={(date) => setData('wife.tarikh_lahir', date)}
                        maxDate={new Date()}
                        placeholder="Pilih tarikh lahir"
                    />
                    {data.tarikh_lahir && (
                        <p className="text-sm text-muted-foreground">
                            Umur: {calculateAge(data.tarikh_lahir)} tahun
                        </p>
                    )}
                </div>

                <div className="grid gap-2">
                    <Label htmlFor="wife_no_hp">Nombor Telefon</Label>
                    <Input
                        id="wife_no_hp"
                        value={data.no_hp}
                        onChange={e => setData('wife.no_hp', e.target.value)}
                        placeholder="Contoh: 0123456789"
                        maxLength={15}
                    />
                </div>

                <div className="grid gap-2">
                    <Label htmlFor="wife_status">Status</Label>
                    <Select
                        value={data.status}
                        onValueChange={(value) => setData('wife.status', value)}
                    >
                        <SelectTrigger>
                            <SelectValue placeholder="Pilih status" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="berkahwin">Berkahwin</SelectItem>
                            <SelectItem value="bujang">Bujang</SelectItem>
                            <SelectItem value="janda">Janda</SelectItem>
                            <SelectItem value="bercerai">Bercerai</SelectItem>
                        </SelectContent>
                    </Select>
                </div>

                <div className="grid gap-2">
                    <Label htmlFor="wife_pendidikan">Tahap Pendidikan</Label>
                    <Select
                        value={data.pendidikan}
                        onValueChange={(value) => setData('wife.pendidikan', value)}
                    >
                        <SelectTrigger>
                            <SelectValue placeholder="Pilih tahap pendidikan" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="tiada_pendidikan">Tiada Pendidikan</SelectItem>
                            <SelectItem value="sekolah_dewasa">Sekolah Dewasa</SelectItem>
                            <SelectItem value="sekolah_pondok_agama">Sekolah Pondok/Agama</SelectItem>
                            <SelectItem value="tadika_prasekolah">Tadika/PraSekolah</SelectItem>
                            <SelectItem value="sekolah_rendah_kebangsaan">Sekolah Rendah Kebangsaan</SelectItem>
                            <SelectItem value="pt3_pmr_srp">PT3/PMR/SRP</SelectItem>
                            <SelectItem value="spm">SPM</SelectItem>
                            <SelectItem value="stpm">STPM</SelectItem>
                            <SelectItem value="sijil_kemahiran_bertauliah">Sijil Kemahiran Bertauliah</SelectItem>
                            <SelectItem value="diploma_setaraf">Diploma/Setaraf</SelectItem>
                            <SelectItem value="ijazah_ke_atas">Ijazah ke Atas</SelectItem>
                        </SelectContent>
                    </Select>
                </div>

                <div className="grid gap-2">
                    <Label htmlFor="wife_pekerjaan">Pekerjaan</Label>
                    <Select
                        value={data.pekerjaan}
                        onValueChange={(value) => {
                            setData('wife.pekerjaan', value);
                            if (value !== 'lain_lain') {
                                setData('wife.pekerjaan_lain', '');
                            }
                        }}
                    >
                        <SelectTrigger>
                            <SelectValue placeholder="Pilih pekerjaan" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="penjawat_awam">Penjawat Awam</SelectItem>
                            <SelectItem value="pekerja_swasta">Pekerja Swasta</SelectItem>
                            <SelectItem value="petani">Petani</SelectItem>
                            <SelectItem value="nelayan">Nelayan</SelectItem>
                            <SelectItem value="usahawan">Usahawan</SelectItem>
                            <SelectItem value="surirumah_sepenuh_masa">Surirumah Sepenuh Masa</SelectItem>
                            <SelectItem value="pesara">Pesara</SelectItem>
                            <SelectItem value="penganggur_belia">Penganggur - Belia (Tiada Kemahiran)</SelectItem>
                            <SelectItem value="penganggur_graduan">Penganggur - Graduan</SelectItem>
                            <SelectItem value="lain_lain">Lain-lain (Nyatakan)</SelectItem>
                        </SelectContent>
                    </Select>
                    {data.pekerjaan === 'lain_lain' && (
                        <Input
                            id="wife_pekerjaan_lain"
                            value={data.pekerjaan_lain}
                            onChange={e => setData('wife.pekerjaan_lain', e.target.value)}
                            placeholder="Nyatakan pekerjaan lain"
                            className="mt-2"
                        />
                    )}
                </div>

                <div className="grid gap-2">
                    <Label htmlFor="wife_agama">Agama</Label>
                    <Select
                        value={data.agama}
                        onValueChange={(value) => {
                            setData('wife.agama', value);
                            if (value !== 'lain-lain') {
                                setData('wife.agama_lain', '');
                            }
                        }}
                    >
                        <SelectTrigger>
                            <SelectValue placeholder="Pilih agama" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="islam">Islam</SelectItem>
                            <SelectItem value="kristian">Kristian</SelectItem>
                            <SelectItem value="buddha">Buddha</SelectItem>
                            <SelectItem value="hindu">Hindu</SelectItem>
                            <SelectItem value="lain-lain">Lain-lain</SelectItem>
                        </SelectContent>
                    </Select>
                    {data.agama === 'lain-lain' && (
                        <Input
                            id="wife_agama_lain"
                            value={data.agama_lain}
                            onChange={e => setData('wife.agama_lain', e.target.value)}
                            placeholder="Nyatakan agama lain"
                            className="mt-2"
                        />
                    )}
                </div>

                <div className="grid gap-2">
                    <Label htmlFor="wife_bangsa">Bangsa</Label>
                    <Select
                        value={data.bangsa}
                        onValueChange={(value) => setData('wife.bangsa', value)}
                    >
                        <SelectTrigger>
                            <SelectValue placeholder="Pilih bangsa" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="melayu">Melayu</SelectItem>
                            <SelectItem value="cina">Cina</SelectItem>
                            <SelectItem value="india">India</SelectItem>
                            <SelectItem value="bumiputera">Bumiputera</SelectItem>
                            <SelectItem value="lain-lain">Lain-lain</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
            </div>

            <div className="flex items-center space-x-2">
                <Checkbox
                    id="wife_status_oku"
                    checked={data.status_oku}
                    onCheckedChange={(checked) => setData('wife.status_oku', Boolean(checked))}
                />
                <Label htmlFor="wife_status_oku">Status OKU</Label>
            </div>
        </div>
    );
}
