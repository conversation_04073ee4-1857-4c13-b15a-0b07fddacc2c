<?php

namespace App\Http\Controllers;

use App\Models\Isteri;
use Illuminate\Http\Request;

class IsteriController extends Controller
{
    public function index()
    {
        return response()->json(Isteri::latest()->get());
    }

    public function store(Request $request)
    {
        $request->validate([
            'father_id' => 'required|exists:keluarga,id',
            'nama' => 'required|string|max:255',
            'tarikh_lahir' => 'required|date',
            'no_hp' => 'required|string|max:15',
            'status' => 'required|string|max:255',
            'pendidikan' => 'required|string|max:255',
            'pekerjaan' => 'required|string|max:255',
            'agama' => 'required|string|max:255',
            'bangsa' => 'required|string|max:255',
            'status_oku' => 'required|boolean',
        ]);

        $isteri = Isteri::create($request->all());

        return response()->json($isteri, 201);
    }
}
