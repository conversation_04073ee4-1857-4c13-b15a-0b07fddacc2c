import { useState } from 'react';
import { Head, useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function UpdateData() {
    const [step, setStep] = useState<'validate' | 'confirm'>('validate');
    const [error, setError] = useState<string>('');
    const [loading, setLoading] = useState(false);
    const [familyData, setFamilyData] = useState<{ id: number; nama: string; no_hp: string; tarikh_lahir: string; lot?: string } | null>(null);

    // Form for phone number validation
    const validateForm = useForm({
        no_hp: '',
    });

    // Handle phone number validation
    const handleValidatePhone = async () => {
        if (!validateForm.data.no_hp) {
            setError('Sila masukkan nombor telefon anda.');
            return;
        }

        setLoading(true);
        setError('');

        try {
            const response = await fetch('/api/keluarga/search-by-phone', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ no_hp: validateForm.data.no_hp }),
            });

            const result = await response.json();

            if (response.ok && result.data) {
                setFamilyData(result.data);
                setStep('confirm');
            } else {
                setError('Tiada data dijumpai untuk nombor telefon ini. Sila semak nombor telefon anda.');
            }
        } catch {
            setError('Ralat berlaku semasa mencari data. Sila cuba lagi.');
        } finally {
            setLoading(false);
        }
    };

    const handleConfirmIdentity = (confirmed: boolean) => {
        if (confirmed && familyData) {
            // Redirect to edit page
            window.location.href = `/keluarga/${familyData.id}/edit`;
        } else {
            setStep('validate');
            setFamilyData(null);
            validateForm.reset();
        }
    };

    return (
        <>
            <Head title="Kemaskini Data" />
            
            <div className="min-h-screen bg-gray-50 py-12 px-4">
                <div className="container mx-auto">
                    <Card className="max-w-2xl mx-auto">
                        <CardHeader className="text-center">
                            <CardTitle className="text-2xl">Kemaskini Data Keluarga</CardTitle>
                            <CardDescription>
                                Kemaskini maklumat keluarga anda dengan mengikuti langkah-langkah berikut
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            {step === 'validate' && (
                                <div className="space-y-6">
                                    <div className="text-center">
                                        <h3 className="text-lg font-semibold mb-2">Langkah 1: Pengesahan Nombor Telefon</h3>
                                        <p className="text-muted-foreground">
                                            Masukkan nombor telefon yang anda gunakan semasa mendaftar
                                        </p>
                                    </div>
                                    
                                    <div className="max-w-md mx-auto space-y-4">
                                        <div className="grid gap-2">
                                            <Label htmlFor="validate_no_hp">Nombor Telefon</Label>
                                            <Input
                                                id="validate_no_hp"
                                                type="tel"
                                                value={validateForm.data.no_hp}
                                                onChange={e => validateForm.setData('no_hp', e.target.value)}
                                                placeholder="Contoh: 0123456789"
                                                maxLength={15}
                                            />
                                        </div>
                                        
                                        {error && (
                                            <Alert variant="destructive">
                                                <AlertDescription>{error}</AlertDescription>
                                            </Alert>
                                        )}
                                        
                                        <Button 
                                            onClick={handleValidatePhone} 
                                            className="w-full"
                                            disabled={loading || !validateForm.data.no_hp}
                                        >
                                            {loading ? 'Mencari...' : 'Cari Data'}
                                        </Button>
                                    </div>
                                </div>
                            )}

                            {step === 'confirm' && familyData && (
                                <div className="space-y-6">
                                    <div className="text-center">
                                        <h3 className="text-lg font-semibold mb-2">Langkah 2: Pengesahan Identiti</h3>
                                        <p className="text-muted-foreground">
                                            Sila sahkan identiti anda
                                        </p>
                                    </div>
                                    
                                    <div className="max-w-md mx-auto">
                                        <Card>
                                            <CardContent className="p-6 text-center">
                                                <h4 className="text-xl font-semibold mb-4">
                                                    Adakah anda <span className="text-primary">{familyData.nama}</span>?
                                                </h4>
                                                
                                                <div className="space-y-2 text-sm text-muted-foreground mb-6">
                                                    <p>Nombor Telefon: {familyData.no_hp}</p>
                                                    <p>Tarikh Lahir: {new Date(familyData.tarikh_lahir).toLocaleDateString('ms-MY')}</p>
                                                    {familyData.lot && <p>Lot: {familyData.lot}</p>}
                                                </div>
                                                
                                                <div className="flex space-x-4">
                                                    <Button 
                                                        onClick={() => handleConfirmIdentity(true)}
                                                        className="flex-1"
                                                    >
                                                        Ya, Saya
                                                    </Button>
                                                    <Button 
                                                        variant="outline"
                                                        onClick={() => handleConfirmIdentity(false)}
                                                        className="flex-1"
                                                    >
                                                        Bukan Saya
                                                    </Button>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </>
    );
}
