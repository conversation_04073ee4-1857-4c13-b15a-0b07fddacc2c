import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface IptData {
    id: number;
    anak_id: number;
    tajaan: 'swasta' | 'kerajaan' | 'sendiri';
    created_at: string;
    updated_at: string;
}

interface AnakData {
    id: number;
    father_id: number;
    nama: string;
    jantina: 'lelaki' | 'perempuan';
    tarikh_lahir: string;
    no_hp: string;
    status: string;
    pendidikan: string;
    pekerjaan: string | null;
    pekerjaan_lain: string | null;
    agama: string;
    agama_lain: string | null;
    bangsa: string;
    status_oku: boolean;
    sedang_belajar_ipt: boolean;
    ipt?: IptData | null;
}

interface IsteriData {
    id: number;
    father_id: number;
    nama: string;
    tarikh_lahir: string;
    no_hp: string;
    status: string;
    pendidikan: string;
    pekerjaan: string;
    pekerjaan_lain: string | null;
    agama: string;
    agama_lain: string | null;
    bangsa: string;
    status_oku: boolean;
}

interface KeluargaData {
    id: number;
    lot: number | null;
    bil_rumah: number | null;
    nama: string;
    tarikh_lahir: string;
    no_hp: string;
    status: string;
    pendidikan: string;
    pekerjaan: string;
    pekerjaan_lain: string | null;
    agama: string;
    agama_lain: string | null;
    bangsa: string;
    status_oku: boolean;
    'e-kasih': boolean;
    pprt: boolean;
    pendapatan: number | null;
    created_at: string;
    updated_at: string;
    isteri?: IsteriData | null;
    anak: AnakData[];
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

export default function Dashboard() {
    const [keluargaData, setKeluargaData] = useState<KeluargaData[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        fetchKeluargaData();
    }, []);

    const fetchKeluargaData = async () => {
        try {
            const response = await fetch('/keluarga');
            if (!response.ok) {
                throw new Error('Failed to fetch keluarga data');
            }
            const data = await response.json();
            setKeluargaData(data);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'An error occurred');
        } finally {
            setLoading(false);
        }
    };

    const calculateAge = (birthDate: string): number => {
        const today = new Date();
        const birth = new Date(birthDate);
        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--;
        }
        
        return age;
    };

    const formatDate = (dateString: string): string => {
        return new Date(dateString).toLocaleDateString('ms-MY', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const formatCurrency = (amount: number | null): string => {
        if (amount === null) return 'Tidak dinyatakan';
        return new Intl.NumberFormat('ms-MY', {
            style: 'currency',
            currency: 'MYR'
        }).format(amount);
    };

    const cleanDisplayValue = (value: string | number | null | undefined): string => {
        // Handle null, undefined, empty string, or "0" values
        if (value === null || value === undefined || value === '' || value === '0' || value === 0) {
            return 'Tidak dinyatakan';
        }
        
        const stringValue = String(value).trim();
        if (stringValue === '' || stringValue === '0') {
            return 'Tidak dinyatakan';
        }
        
        return stringValue;
    };

    const cleanPhoneNumber = (phone: string | null | undefined): string => {
        if (!phone || phone === '0' || phone.trim() === '') {
            return 'Tidak dinyatakan';
        }
        return phone;
    };

    const isTrulyTrue = (value: unknown): boolean => {
        return value === true || value === 1 || value === '1';
    };

    if (loading) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Dashboard" />
                <div className="flex items-center justify-center min-h-[400px]">
                    <div className="text-center">
                        <div className="w-8 h-8 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin mx-auto mb-4"></div>
                        <p className="text-gray-600">Memuat data...</p>
                    </div>
                </div>
            </AppLayout>
        );
    }

    if (error) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Dashboard" />
                <div className="flex items-center justify-center min-h-[400px]">
                    <div className="text-center">
                        <p className="text-red-600 mb-2">Error: {error}</p>
                        <button 
                            onClick={fetchKeluargaData}
                            className="px-4 py-2 border border-gray-300 rounded text-sm hover:bg-gray-50"
                        >
                            Cuba Lagi
                        </button>
                    </div>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            
            <div className="space-y-8 p-6">
                {/* Header */}
                <div className="mb-8">
                    <h1 className="text-2xl font-semibold text-gray-900">Dashboard Keluarga</h1>
                    <p className="text-gray-600 mt-2">Maklumat keluarga dalam sistem</p>
                </div>

                {/* Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <Card className="shadow-sm">
                        <CardContent className="p-6">
                            <div className="text-center">
                                <p className="text-3xl font-semibold text-gray-900">{keluargaData.length}</p>
                                <p className="text-sm text-gray-600 mt-2">Jumlah Keluarga</p>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card className="shadow-sm">
                        <CardContent className="p-6">
                            <div className="text-center">
                                <p className="text-3xl font-semibold text-gray-900">
                                    {keluargaData.reduce((total, keluarga) => total + keluarga.anak.length, 0)}
                                </p>
                                <p className="text-sm text-gray-600 mt-2">Jumlah Anak</p>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card className="shadow-sm">
                        <CardContent className="p-6">
                            <div className="text-center">
                                <p className="text-3xl font-semibold text-gray-900">
                                    {keluargaData.reduce((total, keluarga) => 
                                        total + keluarga.anak.filter(anak => isTrulyTrue(anak.sedang_belajar_ipt)).length, 0
                                    )}
                                </p>
                                <p className="text-sm text-gray-600 mt-2">Pelajar IPT</p>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card className="shadow-sm">
                        <CardContent className="p-6">
                            <div className="text-center">
                                <p className="text-3xl font-semibold text-gray-900">
                                    {keluargaData.filter(keluarga => 
                                        isTrulyTrue(keluarga.status_oku) || 
                                        isTrulyTrue(keluarga.isteri?.status_oku) || 
                                        keluarga.anak.some(anak => isTrulyTrue(anak.status_oku))
                                    ).length}
                                </p>
                                <p className="text-sm text-gray-600 mt-2">Keluarga OKU</p>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Family Data */}
                <div className="space-y-8">
                    {keluargaData.map((keluarga) => (
                        <Card key={keluarga.id} className="shadow-sm">
                            <CardHeader className="pb-6 px-6 pt-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <CardTitle className="text-xl font-semibold">
                                            Keluarga {keluarga.nama}
                                        </CardTitle>
                                        <CardDescription className="flex items-center gap-4 mt-2 text-gray-600">
                                            {keluarga.lot && keluarga.bil_rumah && (
                                                <span>Lot {keluarga.lot}, Bil. Rumah {keluarga.bil_rumah}</span>
                                            )}
                                            <span>Umur: {calculateAge(keluarga.tarikh_lahir)} tahun</span>
                                        </CardDescription>
                                    </div>
                                    <div className="flex gap-2">
                                        {isTrulyTrue(keluarga['e-kasih']) && (
                                            <Badge variant="secondary">E-Kasih</Badge>
                                        )}
                                        {isTrulyTrue(keluarga.pprt) && (
                                            <Badge variant="secondary">PPRT</Badge>
                                        )}
                                        {isTrulyTrue(keluarga.status_oku) && (
                                            <Badge variant="secondary">OKU</Badge>
                                        )}
                                    </div>
                                </div>
                            </CardHeader>
                            
                            <CardContent className="px-6 pb-6">
                                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                                    {/* Family Head */}
                                    <div className="bg-gray-50 rounded-lg p-4">
                                        <h3 className="font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">
                                            Ketua Keluarga
                                        </h3>
                                        <div className="space-y-3 text-sm">
                                            <div className="flex justify-between">
                                                <span className="text-gray-600">Nama</span>
                                                <span className="font-medium">{keluarga.nama}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-gray-600">Tarikh Lahir</span>
                                                <span>{formatDate(keluarga.tarikh_lahir)}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-gray-600">No. HP</span>
                                                <span>{cleanPhoneNumber(keluarga.no_hp)}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-gray-600">Pendidikan</span>
                                                <span>{cleanDisplayValue(keluarga.pendidikan)}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-gray-600">Pekerjaan</span>
                                                <span>{cleanDisplayValue(keluarga.pekerjaan)}</span>
                                            </div>
                                                                                         <div className="flex justify-between">
                                                 <span className="text-gray-600">Agama</span>
                                                 <span>{cleanDisplayValue(keluarga.agama)}</span>
                                             </div>
                                             <div className="flex justify-between">
                                                 <span className="text-gray-600">Bangsa</span>
                                                 <span>{cleanDisplayValue(keluarga.bangsa)}</span>
                                             </div>
                                            <div className="flex justify-between">
                                                <span className="text-gray-600">Pendapatan</span>
                                                <span className="font-medium">{formatCurrency(keluarga.pendapatan)}</span>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Wife */}
                                    <div className="bg-gray-50 rounded-lg p-4">
                                        <h3 className="font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">
                                            Isteri
                                        </h3>
                                        {keluarga.isteri ? (
                                            <div className="space-y-3 text-sm">
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600">Nama</span>
                                                    <span className="font-medium">{keluarga.isteri.nama}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600">Tarikh Lahir</span>
                                                    <span>{formatDate(keluarga.isteri.tarikh_lahir)}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600">No. HP</span>
                                                    <span>{cleanPhoneNumber(keluarga.isteri.no_hp)}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600">Pendidikan</span>
                                                    <span>{cleanDisplayValue(keluarga.isteri.pendidikan)}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600">Pekerjaan</span>
                                                    <span>{cleanDisplayValue(keluarga.isteri.pekerjaan)}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600">Agama</span>
                                                    <span>{cleanDisplayValue(keluarga.isteri.agama)}</span>
                                                </div>
                                                {isTrulyTrue(keluarga.isteri.status_oku) && (
                                                    <div className="mt-2">
                                                        <Badge variant="secondary" className="text-xs">OKU</Badge>
                                                    </div>
                                                )}
                                            </div>
                                        ) : (
                                            <p className="text-gray-500 text-sm">Tiada maklumat isteri</p>
                                        )}
                                    </div>

                                    {/* Children */}
                                    <div className="bg-gray-50 rounded-lg p-4">
                                        <h3 className="font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">
                                            Anak-anak ({keluarga.anak.length})
                                        </h3>
                                        {keluarga.anak.length > 0 ? (
                                            <div className="space-y-4">
                                                {keluarga.anak.map((anak, index) => (
                                                    <div key={anak.id} className="p-3 border rounded text-sm">
                                                        <div className="flex items-center justify-between mb-2">
                                                            <span className="font-medium">
                                                                {index + 1}. {anak.nama}
                                                            </span>
                                                            <div className="flex gap-1">
                                                                <Badge variant="outline" className="text-xs">
                                                                    {anak.jantina}
                                                                </Badge>
                                                                {isTrulyTrue(anak.status_oku) && (
                                                                    <Badge variant="secondary" className="text-xs">OKU</Badge>
                                                                )}
                                                            </div>
                                                        </div>
                                                             <div className="space-y-1 text-xs text-gray-600">
                                                             <div className="flex justify-between">
                                                                 <span>Umur:</span>
                                                                 <span>{calculateAge(anak.tarikh_lahir)} tahun</span>
                                                             </div>
                                                             <div className="flex justify-between">
                                                                 <span>Pendidikan:</span>
                                                                 <span>{cleanDisplayValue(anak.pendidikan)}</span>
                                                             </div>
                                                             <div className="flex justify-between">
                                                                 <span>Agama:</span>
                                                                 <span>{cleanDisplayValue(anak.agama)}</span>
                                                             </div>
                                                             {anak.pekerjaan && cleanDisplayValue(anak.pekerjaan) !== 'Tidak dinyatakan' && (
                                                                 <div className="flex justify-between">
                                                                     <span>Pekerjaan:</span>
                                                                     <span>{cleanDisplayValue(anak.pekerjaan)}</span>
                                                                 </div>
                                                             )}
                                                         </div>
                                                        {isTrulyTrue(anak.sedang_belajar_ipt) && anak.ipt && (
                                                            <div className="mt-2 pt-2 border-t">
                                                                <div className="flex items-center justify-between">
                                                                    <span className="text-xs text-blue-600">Pelajar IPT</span>
                                                                    <Badge variant="outline" className="text-xs">
                                                                        Tajaan {anak.ipt.tajaan}
                                                                    </Badge>
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>
                                                ))}
                                            </div>
                                        ) : (
                                            <p className="text-gray-500 text-sm">Tiada maklumat anak</p>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {keluargaData.length === 0 && (
                    <div className="text-center py-12">
                        <p className="text-gray-500">Tiada data keluarga</p>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}

