import { useState } from 'react';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface AlertState {
    isOpen: boolean;
    title: string;
    message: string;
    onConfirm?: () => void;
}

export function useAlertDialog() {
    const [alertState, setAlertState] = useState<AlertState>({
        isOpen: false,
        title: '',
        message: '',
    });

    const showAlert = (title: string, message: string, onConfirm?: () => void) => {
        setAlertState({
            isOpen: true,
            title,
            message,
            onConfirm,
        });
    };

    const hideAlert = () => {
        setAlertState({
            isOpen: false,
            title: '',
            message: '',
        });
    };

    const AlertDialogComponent = () => (
        <AlertDialog open={alertState.isOpen} onOpenChange={hideAlert}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>{alertState.title}</AlertDialogTitle>
                    <AlertDialogDescription className="whitespace-pre-line">
                        {alertState.message}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogAction 
                        onClick={() => {
                            if (alertState.onConfirm) {
                                alertState.onConfirm();
                            }
                            hideAlert();
                        }}
                    >
                        OK
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );

    return {
        showAlert,
        AlertDialogComponent,
    };
} 