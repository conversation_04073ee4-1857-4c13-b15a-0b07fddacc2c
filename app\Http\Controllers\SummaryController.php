<?php

namespace App\Http\Controllers;

use App\Models\Keluarga;
use App\Models\Isteri;
use App\Models\Anak;
use App\Models\Ipt;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SummaryController extends Controller
{
    public function index()
    {
        $data = $this->generateSummaryData();
        return response()->json($data);
    }

    private function generateSummaryData()
    {
        $families = Keluarga::with(['isteri', 'anak.ipt'])->get();
        
        return [
            'demographic' => $this->getPopulationData($families),
            'ethnicity' => $this->getEthnicityData($families),
            'education' => $this->getEducationData($families),
            'employment' => $this->getEmploymentData($families),
            'income' => $this->getIncomeData($families),
            'special_groups' => $this->getSpecialGroupsData($families),
            'generated_at' => now()->format('Y-m-d H:i:s')
        ];
    }

    private function getPopulationData($families)
    {
        $totalPopulation = 0;
        $religionCounts = ['islam' => 0, 'kristian' => 0, 'buddha' => 0, 'hindu' => 0, 'lain' => 0];
        $ageCategoriesMale = ['below_12' => 0, '13_to_20' => 0, '21_to_35' => 0, '36_to_59' => 0, '60_above' => 0];
        $ageCategoriesFemale = ['below_12' => 0, '13_to_20' => 0, '21_to_35' => 0, '36_to_59' => 0, '60_above' => 0];

        foreach ($families as $family) {
            $totalPopulation++; // Family head (male)
            $this->categorizeByAge($family->tarikh_lahir, $ageCategoriesMale);
            $this->categorizeByReligion($family->agama, $religionCounts);
            
            if ($family->isteri) {
                $totalPopulation++; // Wife
                $this->categorizeByAge($family->isteri->tarikh_lahir, $ageCategoriesFemale);
                $this->categorizeByReligion($family->isteri->agama, $religionCounts);
            }
            
            foreach ($family->anak as $child) {
                $totalPopulation++; // Children
                if ($child->jantina === 'lelaki') {
                    $this->categorizeByAge($child->tarikh_lahir, $ageCategoriesMale);
                } else {
                    $this->categorizeByAge($child->tarikh_lahir, $ageCategoriesFemale);
                }
                $this->categorizeByReligion($child->agama, $religionCounts);
            }
        }

        return [
            'total_population' => $totalPopulation,
            'total_families' => $families->count(),
            'total_houses' => $families->whereNotNull('bil_rumah')->sum('bil_rumah'),
            'religion_counts' => $religionCounts,
            'age_categories_male' => $ageCategoriesMale,
            'age_categories_female' => $ageCategoriesFemale
        ];
    }

    private function getEthnicityData($families)
    {
        $ethnicityMale = [];
        $ethnicityFemale = [];
        
        foreach ($families as $family) {
            $ethnicity = $family->bangsa ?? 'Tidak Dinyatakan';
            $ethnicityMale[$ethnicity] = ($ethnicityMale[$ethnicity] ?? 0) + 1;
            
            if ($family->isteri) {
                $wifeEthnicity = $family->isteri->bangsa ?? 'Tidak Dinyatakan';
                $ethnicityFemale[$wifeEthnicity] = ($ethnicityFemale[$wifeEthnicity] ?? 0) + 1;
            }
            
            foreach ($family->anak as $child) {
                $childEthnicity = $child->bangsa ?? 'Tidak Dinyatakan';
                if ($child->jantina === 'lelaki') {
                    $ethnicityMale[$childEthnicity] = ($ethnicityMale[$childEthnicity] ?? 0) + 1;
                } else {
                    $ethnicityFemale[$childEthnicity] = ($ethnicityFemale[$childEthnicity] ?? 0) + 1;
                }
            }
        }

        return ['male' => $ethnicityMale, 'female' => $ethnicityFemale];
    }

    private function getEducationData($families)
    {
        $educationLevels = [
            'tiada_pendidikan' => ['male' => 0, 'female' => 0],
            'sekolah_rendah' => ['male' => 0, 'female' => 0],
            'spm' => ['male' => 0, 'female' => 0],
            'stpm' => ['male' => 0, 'female' => 0],
            'diploma' => ['male' => 0, 'female' => 0],
            'ijazah' => ['male' => 0, 'female' => 0]
        ];

        foreach ($families as $family) {
            $this->categorizeByEducation($family->pendidikan, 'male', $educationLevels);
            
            if ($family->isteri) {
                $this->categorizeByEducation($family->isteri->pendidikan, 'female', $educationLevels);
            }
            
            foreach ($family->anak as $child) {
                $gender = $child->jantina === 'lelaki' ? 'male' : 'female';
                $this->categorizeByEducation($child->pendidikan, $gender, $educationLevels);
            }
        }

        return $educationLevels;
    }

    private function getEmploymentData($families)
    {
        $employmentTypes = [
            'penjawat_awam' => ['male' => 0, 'female' => 0],
            'pekerja_swasta' => ['male' => 0, 'female' => 0],
            'petani' => ['male' => 0, 'female' => 0],
            'usahawan' => ['male' => 0, 'female' => 0],
            'surirumah' => ['male' => 0, 'female' => 0],
            'penganggur' => ['male' => 0, 'female' => 0]
        ];

        foreach ($families as $family) {
            $this->categorizeByEmployment($family->pekerjaan, 'male', $employmentTypes);
            
            if ($family->isteri) {
                $this->categorizeByEmployment($family->isteri->pekerjaan, 'female', $employmentTypes);
            }
            
            foreach ($family->anak as $child) {
                $age = Carbon::parse($child->tarikh_lahir)->age;
                if ($age >= 18) {
                    $gender = $child->jantina === 'lelaki' ? 'male' : 'female';
                    $this->categorizeByEmployment($child->pekerjaan, $gender, $employmentTypes);
                }
            }
        }

        return $employmentTypes;
    }

    private function getIncomeData($families)
    {
        $incomeCategories = ['top_20' => 0, 'middle_40' => 0, 'bottom_40' => 0];

        foreach ($families as $family) {
            if ($family->pendapatan) {
                if ($family->pendapatan >= 8319) {
                    $incomeCategories['top_20']++;
                } elseif ($family->pendapatan >= 3860) {
                    $incomeCategories['middle_40']++;
                } else {
                    $incomeCategories['bottom_40']++;
                }
            }
        }

        return $incomeCategories;
    }

    private function getSpecialGroupsData($families)
    {
        $specialGroups = [
            'oku_total' => 0,
            'single_parents' => 0,
            'orphans' => 0
        ];

        foreach ($families as $family) {
            if ($family->status_oku) $specialGroups['oku_total']++;
            if ($family->isteri && $family->isteri->status_oku) $specialGroups['oku_total']++;
            if (!$family->isteri) $specialGroups['single_parents']++;
            
            foreach ($family->anak as $child) {
                if ($child->status_oku) $specialGroups['oku_total']++;
            }
        }

        return $specialGroups;
    }

    // Helper methods
    private function categorizeByAge($birthDate, &$categories)
    {
        $age = Carbon::parse($birthDate)->age;
        
        if ($age <= 12) $categories['below_12']++;
        elseif ($age >= 13 && $age <= 20) $categories['13_to_20']++;
        elseif ($age >= 21 && $age <= 35) $categories['21_to_35']++;
        elseif ($age >= 36 && $age <= 59) $categories['36_to_59']++;
        else $categories['60_above']++;
    }

    private function categorizeByReligion($agama, &$counts)
    {
        $religion = strtolower($agama ?? '');
        
        if (strpos($religion, 'islam') !== false) $counts['islam']++;
        elseif (strpos($religion, 'kristian') !== false) $counts['kristian']++;
        elseif (strpos($religion, 'buddha') !== false) $counts['buddha']++;
        elseif (strpos($religion, 'hindu') !== false) $counts['hindu']++;
        else $counts['lain']++;
    }

    private function categorizeByEducation($pendidikan, $gender, &$levels)
    {
        $education = strtolower($pendidikan ?? '');
        
        if (empty($education)) $levels['tiada_pendidikan'][$gender]++;
        elseif (strpos($education, 'spm') !== false) $levels['spm'][$gender]++;
        elseif (strpos($education, 'diploma') !== false) $levels['diploma'][$gender]++;
        elseif (strpos($education, 'ijazah') !== false) $levels['ijazah'][$gender]++;
        elseif (strpos($education, 'stpm') !== false) $levels['stpm'][$gender]++;
        elseif (strpos($education, 'rendah') !== false) $levels['sekolah_rendah'][$gender]++;
        else $levels['tiada_pendidikan'][$gender]++;
    }

    private function categorizeByEmployment($pekerjaan, $gender, &$types)
    {
        $job = strtolower($pekerjaan ?? '');
        
        if (strpos($job, 'kerajaan') !== false || strpos($job, 'awam') !== false) {
            $types['penjawat_awam'][$gender]++;
        } elseif (strpos($job, 'swasta') !== false) {
            $types['pekerja_swasta'][$gender]++;
        } elseif (strpos($job, 'petani') !== false) {
            $types['petani'][$gender]++;
        } elseif (strpos($job, 'usaha') !== false) {
            $types['usahawan'][$gender]++;
        } elseif (strpos($job, 'suri') !== false) {
            $types['surirumah'][$gender]++;
        } else {
            $types['penganggur'][$gender]++;
        }
    }
} 