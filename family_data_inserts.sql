-- SQL INSERT statements for LOT 368 Family Data
-- Since the family head has passed away, the wife (<PERSON><PERSON>) becomes the family head

-- INSERT into keluarga table (<PERSON><PERSON> as family head)
INSERT INTO keluarga (
    lot, 
    bil_rumah, 
    nama, 
    tarikh_lahir, 
    no_hp, 
    status, 
    pendidikan, 
    peker<PERSON><PERSON>, 
    peker<PERSON><PERSON>_lain, 
    agama, 
    agama_lain, 
    bangsa, 
    status_oku, 
    `e-kasih`, 
    pprt, 
    pendapatan, 
    created_at, 
    updated_at
) VALUES (
    368, 
    1, 
    '<PERSON><PERSON> binti <PERSON>', 
    '1966-05-05', 
    '0178992050', 
    '<PERSON><PERSON>', 
    '<PERSON><PERSON><PERSON> rendah keb<PERSON>an', 
    '<PERSON><PERSON> rumah', 
    NULL, 
    'Islam', 
    NULL, 
    'Melayu', 
    0, 
    0, 
    0, 
    0.00, 
    NOW(), 
    NOW()
);

-- Get the ID of the inserted family head for foreign key reference
-- In practice, you would use the actual ID returned from the above insert
-- For this example, let's assume the family head ID is 1

-- INSERT children into anak table
-- Child 1: <PERSON><PERSON><PERSON> binti <PERSON><PERSON>i
INSERT INTO anak (
    father_id, 
    nama, 
    jantina, 
    tarikh_lahir, 
    no_hp, 
    status, 
    pendidikan, 
    peker<PERSON><PERSON>, 
    peker<PERSON><PERSON>_lain, 
    agama, 
    agama_lain, 
    bangsa, 
    status_oku, 
    sedang_belajar_ipt, 
    created_at, 
    updated_at
) VALUES (
    1, -- Replace with actual family head ID
    'Ratnawati binti Marsidi', 
    'perempuan', 
    '1987-05-16', 
    NULL, 
    'Ibu tunggal', 
    'STPM', 
    NULL, 
    NULL, 
    'Islam', 
    NULL, 
    'Melayu', 
    0, 
    0, 
    NOW(), 
    NOW()
);

-- Child 2: Masayu binti Marsidi
INSERT INTO anak (
    father_id, 
    nama, 
    jantina, 
    tarikh_lahir, 
    no_hp, 
    status, 
    pendidikan, 
    pekerjaan, 
    pekerjaan_lain, 
    agama, 
    agama_lain, 
    bangsa, 
    status_oku, 
    sedang_belajar_ipt, 
    created_at, 
    updated_at
) VALUES (
    1, -- Replace with actual family head ID
    'Masayu binti Marsidi', 
    'perempuan', 
    '1988-09-02', 
    NULL, 
    'Berkahwin', 
    'SPM', 
    NULL, 
    NULL, 
    'Islam', 
    NULL, 
    'Melayu', 
    0, 
    0, 
    NOW(), 
    NOW()
);

-- Child 3: Mustika binti Marsidi
INSERT INTO anak (
    father_id, 
    nama, 
    jantina, 
    tarikh_lahir, 
    no_hp, 
    status, 
    pendidikan, 
    pekerjaan, 
    pekerjaan_lain, 
    agama, 
    agama_lain, 
    bangsa, 
    status_oku, 
    sedang_belajar_ipt, 
    created_at, 
    updated_at
) VALUES (
    1, -- Replace with actual family head ID
    'Mustika binti Marsidi', 
    'perempuan', 
    '1990-11-26', 
    NULL, 
    'Berkahwin', 
    'STPM', 
    NULL, 
    NULL, 
    'Islam', 
    NULL, 
    'Melayu', 
    0, 
    0, 
    NOW(), 
    NOW()
);

-- Child 4: Arianto bin Marsidi
INSERT INTO anak (
    father_id, 
    nama, 
    jantina, 
    tarikh_lahir, 
    no_hp, 
    status, 
    pendidikan, 
    pekerjaan, 
    pekerjaan_lain, 
    agama, 
    agama_lain, 
    bangsa, 
    status_oku, 
    sedang_belajar_ipt, 
    created_at, 
    updated_at
) VALUES (
    1, -- Replace with actual family head ID
    'Arianto bin Marsidi', 
    'lelaki', 
    '1994-03-24', 
    NULL, 
    'Berkahwin', 
    'SPM', 
    NULL, 
    NULL, 
    'Islam', 
    NULL, 
    'Melayu', 
    0, 
    0, 
    NOW(), 
    NOW()
);

-- Note: No isteri table insert is needed since Norati is now the family head
-- Note: No ipt table inserts are needed as none of the children are currently studying at IPT

-- =====================================================================================
-- SQL INSERT statements for LOT 397 Family Data (Jalin Kundayong Family)
-- =====================================================================================

-- INSERT into keluarga table (Jalin Kundayong as family head)
INSERT INTO keluarga (
    lot, 
    bil_rumah, 
    nama, 
    tarikh_lahir, 
    no_hp, 
    status, 
    pendidikan, 
    pekerjaan, 
    pekerjaan_lain, 
    agama, 
    agama_lain, 
    bangsa, 
    status_oku, 
    `e-kasih`, 
    pprt, 
    pendapatan, 
    created_at, 
    updated_at
) VALUES (
    397, 
    1, 
    'Jalin Kundayong', 
    '1961-06-01', 
    '016-9743397', 
    'Berkahwin', 
    '', 
    'Bekerja sendiri', 
    NULL, 
    'Kristian', 
    NULL, 
    'Dusun', 
    0, 
    0, 
    0, 
    650.00, 
    NOW(), 
    NOW()
);

-- INSERT into isteri table (Candida Dawai)
INSERT INTO isteri (
    father_id, 
    nama, 
    tarikh_lahir, 
    no_hp, 
    status, 
    pendidikan, 
    pekerjaan, 
    pekerjaan_lain, 
    agama, 
    agama_lain, 
    bangsa, 
    status_oku, 
    created_at, 
    updated_at
) VALUES (
    34, -- Replace with actual family head ID from keluarga insert
    'Candida Dawai', 
    '1972-10-03', 
    '', 
    'Berkahwin', 
    '', 
    'Suri rumah', 
    NULL, 
    'Kristian', 
    NULL, 
    'Dusun', 
    0, 
    NOW(), 
    NOW()
);

-- INSERT children into anak table
-- Child 1: Ricel Jalyeda Jalin
INSERT INTO anak (
    father_id, 
    nama, 
    jantina, 
    tarikh_lahir, 
    no_hp, 
    status, 
    pendidikan, 
    pekerjaan, 
    pekerjaan_lain, 
    agama, 
    agama_lain, 
    bangsa, 
    status_oku, 
    sedang_belajar_ipt, 
    created_at, 
    updated_at
) VALUES (
    34, -- Replace with actual family head ID
    'Ricel Jalyeda Jalin', 
    'perempuan', 
    '1992-04-25', 
    NULL, 
    'Berkahwin', 
    'Ijazah Sarjana Muda', 
    NULL, 
    NULL, 
    'Kristian', 
    NULL, 
    'Dusun', 
    0, 
    0, 
    NOW(), 
    NOW()
);

-- Child 2: Azidaloralyn Jalin
INSERT INTO anak (
    father_id, 
    nama, 
    jantina, 
    tarikh_lahir, 
    no_hp, 
    status, 
    pendidikan, 
    pekerjaan, 
    pekerjaan_lain, 
    agama, 
    agama_lain, 
    bangsa, 
    status_oku, 
    sedang_belajar_ipt, 
    created_at, 
    updated_at
) VALUES (
    34, -- Replace with actual family head ID
    'Azidaloralyn Jalin', 
    'perempuan', 
    '1993-06-27', 
    NULL, 
    'Berkahwin', 
    'Ijazah Sarjana Muda', 
    NULL, 
    NULL, 
    'Kristian', 
    NULL, 
    'Dusun', 
    0, 
    0, 
    NOW(), 
    NOW()
);

-- Child 3: Rozyeidahlyn Jalin
INSERT INTO anak (
    father_id, 
    nama, 
    jantina, 
    tarikh_lahir, 
    no_hp, 
    status, 
    pendidikan, 
    pekerjaan, 
    pekerjaan_lain, 
    agama, 
    agama_lain, 
    bangsa, 
    status_oku, 
    sedang_belajar_ipt, 
    created_at, 
    updated_at
) VALUES (
    34, -- Replace with actual family head ID
    'Rozyeidahlyn Jalin', 
    'perempuan', 
    '1996-08-24', 
    NULL, 
    'Bujang', 
    'SPM', 
    NULL, 
    NULL, 
    'Kristian', 
    NULL, 
    'Dusun', 
    0, 
    0, 
    NOW(), 
    NOW()
);

-- Child 4: Azyana Jalin
INSERT INTO anak (
    father_id, 
    nama, 
    jantina, 
    tarikh_lahir, 
    no_hp, 
    status, 
    pendidikan, 
    pekerjaan, 
    pekerjaan_lain, 
    agama, 
    agama_lain, 
    bangsa, 
    status_oku, 
    sedang_belajar_ipt, 
    created_at, 
    updated_at
) VALUES (
    34, -- Replace with actual family head ID
    'Azyana Jalin', 
    'perempuan', 
    '2000-12-05', 
    NULL, 
    'Berkahwin', 
    'SPM', 
    NULL, 
    NULL, 
    'Muslim', 
    NULL, 
    'Dusun', 
    0, 
    0, 
    NOW(), 
    NOW()
);

-- Child 5: Arven Jalin
INSERT INTO anak (
    father_id, 
    nama, 
    jantina, 
    tarikh_lahir, 
    no_hp, 
    status, 
    pendidikan, 
    pekerjaan, 
    pekerjaan_lain, 
    agama, 
    agama_lain, 
    bangsa, 
    status_oku, 
    sedang_belajar_ipt, 
    created_at, 
    updated_at
) VALUES (
    34, -- Replace with actual family head ID
    'Arven Jalin', 
    'perempuan', 
    '2005-11-22', 
    NULL, 
    'Bujang', 
    'SPM', 
    NULL, 
    NULL, 
    'Kristian', 
    NULL, 
    'Dusun', 
    0, 
    0, 
    NOW(), 
    NOW()
);

-- Child 6: Arvelyn Jalin
INSERT INTO anak (
    father_id, 
    nama, 
    jantina, 
    tarikh_lahir, 
    no_hp, 
    status, 
    pendidikan, 
    pekerjaan, 
    pekerjaan_lain, 
    agama, 
    agama_lain, 
    bangsa, 
    status_oku, 
    sedang_belajar_ipt, 
    created_at, 
    updated_at
) VALUES (
    34, -- Replace with actual family head ID
    'Arvelyn Jalin', 
    'perempuan', 
    '2007-12-15', 
    NULL, 
    'Bujang', 
    'SPM', 
    NULL, 
    NULL, 
    'Kristian', 
    NULL, 
    'Dusun', 
    0, 
    0, 
    NOW(), 
    NOW()
);

-- Note: No ipt table inserts are needed as none of the children are currently studying at IPT

-- =====================================================================================
-- SQL INSERT statements for LOT 342 Family Data (Taing bin Abdul Said - Single Person)
-- =====================================================================================

-- INSERT into keluarga table (Taing bin Abdul Said as single family head)
INSERT INTO keluarga (
    lot, 
    bil_rumah, 
    nama, 
    tarikh_lahir, 
    no_hp, 
    status, 
    pendidikan, 
    pekerjaan, 
    pekerjaan_lain, 
    agama, 
    agama_lain, 
    bangsa, 
    status_oku, 
    `e-kasih`, 
    pprt, 
    pendapatan, 
    created_at, 
    updated_at
) VALUES (
    342, 
    1, 
    'Taing bin Abdul Said', 
    '1959-02-03', 
    '', 
    'Bujang', 
    'Tiada', 
    'Pertani', 
    NULL, 
    'Islam', 
    NULL, 
    'Melayu', 
    0, 
    0, 
    0, 
    300.00, 
    NOW(), 
    NOW()
);

-- Note: No isteri table insert needed - family head is single (bujang)
-- Note: No anak table inserts needed - no children

-- INSERT into keluarga table (Gapar bin Abdul Said - second person at same LOT)
INSERT INTO keluarga (
    lot, 
    bil_rumah, 
    nama, 
    tarikh_lahir, 
    no_hp, 
    status, 
    pendidikan, 
    pekerjaan, 
    pekerjaan_lain, 
    agama, 
    agama_lain, 
    bangsa, 
    status_oku, 
    `e-kasih`, 
    pprt, 
    pendapatan, 
    created_at, 
    updated_at
) VALUES (
    342, 
    1, 
    'Gapar bin Abdul Said', 
    '1962-01-01', 
    '', 
    'Bujang', 
    'Tiada', 
    'Pertani', 
    NULL, 
    'Islam', 
    NULL, 
    'Melayu', 
    0, 
    0, 
    0, 
    300.00, 
    NOW(), 
    NOW()
);

-- Note: No isteri table insert needed - second family head is also single (bujang)
-- Note: No anak table inserts needed - no children

-- =====================================================================================
-- SQL INSERT statements for LOT 378 Family Data (Kaidah bin Abdul Rahim Family)
-- =====================================================================================

-- INSERT into keluarga table (Kaidah bin Abdul Rahim as family head)
INSERT INTO keluarga (
    lot, 
    bil_rumah, 
    nama, 
    tarikh_lahir, 
    no_hp, 
    status, 
    pendidikan, 
    pekerjaan, 
    pekerjaan_lain, 
    agama, 
    agama_lain, 
    bangsa, 
    status_oku, 
    `e-kasih`, 
    pprt, 
    pendapatan, 
    created_at, 
    updated_at
) VALUES (
    378, 
    1, 
    'Kaidah bin Abdul Rahim', 
    '1966-02-21', 
    '01112650481', 
    'Berkahwin', 
    'Sekolah rendah', 
    'Pemandu', 
    NULL, 
    'Islam', 
    NULL, 
    'Tidong', 
    0, 
    1, 
    0, 
    1450.00, 
    NOW(), 
    NOW()
);

-- INSERT into isteri table (Norsidah binti Abd Hamid)
INSERT INTO isteri (
    father_id, 
    nama, 
    tarikh_lahir, 
    no_hp, 
    status, 
    pendidikan, 
    pekerjaan, 
    pekerjaan_lain, 
    agama, 
    agama_lain, 
    bangsa, 
    status_oku, 
    created_at, 
    updated_at
) VALUES (
    4, -- Replace with actual family head ID from keluarga insert
    'Norsidah binti Abd Hamid', 
    '1968-04-02', 
    '0178642469', 
    'Berkahwin', 
    'SPM', 
    'Suri rumah', 
    NULL, 
    'Islam', 
    NULL, 
    'Brunei', 
    0, 
    NOW(), 
    NOW()
);

-- Note: No anak table inserts needed - family has no children (TIADA ANAK) 