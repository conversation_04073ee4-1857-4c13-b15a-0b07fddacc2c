import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { DatePicker } from '@/components/ui/date-picker';

interface KeluargaIsteriFormProps {
    data: {
        // Family Head Data
        lot: string;
        bil_rumah: string;
        nama: string;
        tarikh_lahir: string;
        no_hp: string;
        status: string;
        pendidikan: string;
        pekerjaan: string;
        pekerjaan_lain: string;
        agama: string;
        agama_lain: string;
        bangsa: string;
        status_oku: boolean;
        'e-kasih': boolean;
        pprt: boolean;
        pendapatan: string;
        // Wife Data
        wife: {
            nama: string;
            tarikh_lahir: string;
            no_hp: string;
            status: string;
            pendidikan: string;
            pekerjaan: string;
            pekerjaan_lain: string;
            agama: string;
            agama_lain: string;
            bangsa: string;
            status_oku: boolean;
        };
    };
    setData: (key: string, value: unknown) => void;
    calculateAge: (date: string) => string | number;
}

export default function KeluargaIsteriForm({ data, setData, calculateAge }: KeluargaIsteriFormProps) {
    return (
        <div className="space-y-8">
            {/* Family Head Section */}
            <div className="border p-6 rounded-lg">
                <h3 className="text-lg font-semibold mb-4">Maklumat Ketua Keluarga</h3>
                <div className="space-y-6">
                    <div className="grid gap-4 md:grid-cols-2">
                        <div className="grid gap-2">
                            <Label htmlFor="lot">Lot</Label>
                            <Input
                                id="lot"
                                type="number"
                                value={data.lot}
                                onChange={e => setData('lot', e.target.value)}
                                placeholder="456"
                                max="99999"
                                min="1"
                            />
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="bil_rumah">Bilangan Rumah</Label>
                            <Input
                                id="bil_rumah"
                                type="number"
                                value={data.bil_rumah}
                                onChange={e => setData('bil_rumah', e.target.value)}
                                placeholder="1"
                                max="999"
                                min="1"
                            />
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="nama">Nama Ketua Keluarga</Label>
                            <Input
                                id="nama"
                                value={data.nama}
                                onChange={e => setData('nama', e.target.value)}
                                placeholder="Contoh: Ahmad Bin Ali"
                            />
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="tarikh_lahir">Tarikh Lahir</Label>
                            <DatePicker
                                id="tarikh_lahir"
                                value={data.tarikh_lahir}
                                onChange={(date) => setData('tarikh_lahir', date)}
                                maxDate={new Date()}
                                placeholder="Pilih tarikh lahir"
                            />
                            {data.tarikh_lahir && (
                                <p className="text-sm text-muted-foreground">
                                    Umur: {calculateAge(data.tarikh_lahir)} tahun
                                </p>
                            )}
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="no_hp">Nombor Telefon</Label>
                            <Input
                                id="no_hp"
                                value={data.no_hp}
                                onChange={e => setData('no_hp', e.target.value)}
                                placeholder="Contoh: 0123456789"
                                maxLength={15}
                            />
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="status">Status</Label>
                            <Select
                                value={data.status}
                                onValueChange={(value) => setData('status', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Pilih status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="berkahwin">Berkahwin</SelectItem>
                                    <SelectItem value="bujang">Bujang</SelectItem>
                                    <SelectItem value="duda">Duda</SelectItem>
                                    <SelectItem value="bercerai">Bercerai</SelectItem>
                                    <SelectItem value="balu">Balu</SelectItem>
                                    <SelectItem value="janda">Janda</SelectItem>
                                    <SelectItem value="ibu_tunggal">Ibu Tunggal</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="pendidikan">Tahap Pendidikan</Label>
                            <Select
                                value={data.pendidikan}
                                onValueChange={(value) => setData('pendidikan', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Pilih tahap pendidikan" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="tiada_pendidikan">Tiada Pendidikan</SelectItem>
                                    <SelectItem value="sekolah_dewasa">Sekolah Dewasa</SelectItem>
                                    <SelectItem value="sekolah_pondok_agama">Sekolah Pondok/Agama</SelectItem>
                                    <SelectItem value="tadika_prasekolah">Tadika/PraSekolah</SelectItem>
                                    <SelectItem value="sekolah_rendah_kebangsaan">Sekolah Rendah Kebangsaan</SelectItem>
                                    <SelectItem value="pt3_pmr_srp">PT3/PMR/SRP</SelectItem>
                                    <SelectItem value="spm">SPM</SelectItem>
                                    <SelectItem value="stpm">STPM</SelectItem>
                                    <SelectItem value="sijil_kemahiran_bertauliah">Sijil Kemahiran Bertauliah</SelectItem>
                                    <SelectItem value="diploma_setaraf">Diploma/Setaraf</SelectItem>
                                    <SelectItem value="ijazah_ke_atas">Ijazah ke Atas</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="pekerjaan">Pekerjaan</Label>
                            <Select
                                value={data.pekerjaan}
                                onValueChange={(value) => {
                                    setData('pekerjaan', value);
                                    if (value !== 'lain_lain') {
                                        setData('pekerjaan_lain', '');
                                    }
                                }}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Pilih pekerjaan" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="penjawat_awam">Penjawat Awam</SelectItem>
                                    <SelectItem value="pekerja_swasta">Pekerja Swasta</SelectItem>
                                    <SelectItem value="petani">Petani</SelectItem>
                                    <SelectItem value="nelayan">Nelayan</SelectItem>
                                    <SelectItem value="usahawan">Usahawan</SelectItem>
                                    <SelectItem value="surirumah_sepenuh_masa">Surirumah Sepenuh Masa</SelectItem>
                                    <SelectItem value="pesara">Pesara</SelectItem>
                                    <SelectItem value="penganggur_belia">Penganggur - Belia (Tiada Kemahiran)</SelectItem>
                                    <SelectItem value="penganggur_graduan">Penganggur - Graduan</SelectItem>
                                    <SelectItem value="lain_lain">Lain-lain (Nyatakan)</SelectItem>
                                </SelectContent>
                            </Select>
                            {data.pekerjaan === 'lain_lain' && (
                                <Input
                                    id="pekerjaan_lain"
                                    value={data.pekerjaan_lain}
                                    onChange={e => setData('pekerjaan_lain', e.target.value)}
                                    placeholder="Nyatakan pekerjaan lain"
                                    className="mt-2"
                                />
                            )}
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="agama">Agama</Label>
                            <Select
                                value={data.agama}
                                onValueChange={(value) => {
                                    setData('agama', value);
                                    if (value !== 'lain-lain') {
                                        setData('agama_lain', '');
                                    }
                                }}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Pilih agama" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="islam">Islam</SelectItem>
                                    <SelectItem value="kristian">Kristian</SelectItem>
                                    <SelectItem value="buddha">Buddha</SelectItem>
                                    <SelectItem value="hindu">Hindu</SelectItem>
                                    <SelectItem value="lain-lain">Lain-lain</SelectItem>
                                </SelectContent>
                            </Select>
                            {data.agama === 'lain-lain' && (
                                <Input
                                    id="agama_lain"
                                    value={data.agama_lain}
                                    onChange={e => setData('agama_lain', e.target.value)}
                                    placeholder="Nyatakan agama lain"
                                    className="mt-2"
                                />
                            )}
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="bangsa">Bangsa</Label>
                            <Select
                                value={data.bangsa}
                                onValueChange={(value) => setData('bangsa', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Pilih bangsa" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="melayu">Melayu</SelectItem>
                                    <SelectItem value="cina">Cina</SelectItem>
                                    <SelectItem value="india">India</SelectItem>
                                    <SelectItem value="bumiputera">Bumiputera</SelectItem>
                                    <SelectItem value="lain-lain">Lain-lain</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="pendapatan">Pendapatan Bulanan Sekeluarga (RM)</Label>
                            <Input
                                id="pendapatan"
                                type="number"
                                min="0"
                                step="0.01"
                                value={data.pendapatan}
                                onChange={e => setData('pendapatan', e.target.value)}
                                placeholder="Contoh: 3000.00"
                            />
                            <p className="text-sm text-muted-foreground">Untuk anak yang sudah bekerja dan bekeluarga, jangan kira pendapatan anak, sila isi semula borang ini</p>
                        </div>
                    </div>

                    <div className="grid gap-4 md:grid-cols-3">
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="status_oku"
                                checked={data.status_oku}
                                onCheckedChange={(checked) => setData('status_oku', Boolean(checked))}
                            />
                            <Label htmlFor="status_oku">Status OKU</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="e-kasih"
                                checked={data['e-kasih']}
                                onCheckedChange={(checked) => setData('e-kasih', Boolean(checked))}
                            />
                            <Label htmlFor="e-kasih">E-Kasih</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="pprt"
                                checked={data.pprt}
                                onCheckedChange={(checked) => setData('pprt', Boolean(checked))}
                            />
                            <Label htmlFor="pprt">Program Perumahan Rakyat Termiskin (PPRT)</Label>
                        </div>
                    </div>
                </div>
            </div>

            {/* Wife Section */}
            <div className="border p-6 rounded-lg">
                <h3 className="text-lg font-semibold mb-4">Maklumat Isteri</h3>
                <div className="grid gap-4 md:grid-cols-2">
                    <div className="grid gap-2">
                        <Label htmlFor="wife_nama">Nama Isteri</Label>
                        <Input
                            id="wife_nama"
                            value={data.wife.nama}
                            onChange={e => setData('wife', {
                                ...data.wife,
                                nama: e.target.value
                            })}
                            placeholder="Contoh: Siti Binti Abdullah"
                        />
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="wife_tarikh_lahir">Tarikh Lahir</Label>
                        <DatePicker
                            id="wife_tarikh_lahir"
                            value={data.wife.tarikh_lahir}
                            onChange={(date) => setData('wife', {
                                ...data.wife,
                                tarikh_lahir: date
                            })}
                            maxDate={new Date()}
                            placeholder="Pilih tarikh lahir"
                        />
                        {data.wife.tarikh_lahir && (
                            <p className="text-sm text-muted-foreground">
                                Umur: {calculateAge(data.wife.tarikh_lahir)} tahun
                            </p>
                        )}
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="wife_no_hp">Nombor Telefon</Label>
                        <Input
                            id="wife_no_hp"
                            value={data.wife.no_hp}
                            onChange={e => setData('wife', {
                                ...data.wife,
                                no_hp: e.target.value
                            })}
                            placeholder="Contoh: 0123456789"
                            maxLength={15}
                        />
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="wife_status">Status</Label>
                        <Select
                            value={data.wife.status}
                            onValueChange={(value) => setData('wife', {
                                ...data.wife,
                                status: value
                            })}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Pilih status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="berkahwin">Berkahwin</SelectItem>
                                <SelectItem value="bujang">Bujang</SelectItem>
                                <SelectItem value="janda">Janda</SelectItem>
                                <SelectItem value="duda">Duda</SelectItem>
                                <SelectItem value="bercerai">Bercerai</SelectItem>
                                <SelectItem value="balu">Balu</SelectItem>
                                <SelectItem value="ibu_tunggal">Ibu Tunggal</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="wife_pendidikan">Tahap Pendidikan</Label>
                        <Select
                            value={data.wife.pendidikan}
                            onValueChange={(value) => setData('wife', {
                                ...data.wife,
                                pendidikan: value
                            })}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Pilih tahap pendidikan" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="tiada_pendidikan">Tiada Pendidikan</SelectItem>
                                <SelectItem value="sekolah_dewasa">Sekolah Dewasa</SelectItem>
                                <SelectItem value="sekolah_pondok_agama">Sekolah Pondok/Agama</SelectItem>
                                <SelectItem value="tadika_prasekolah">Tadika/PraSekolah</SelectItem>
                                <SelectItem value="sekolah_rendah_kebangsaan">Sekolah Rendah Kebangsaan</SelectItem>
                                <SelectItem value="pt3_pmr_srp">PT3/PMR/SRP</SelectItem>
                                <SelectItem value="spm">SPM</SelectItem>
                                <SelectItem value="stpm">STPM</SelectItem>
                                <SelectItem value="sijil_kemahiran_bertauliah">Sijil Kemahiran Bertauliah</SelectItem>
                                <SelectItem value="diploma_setaraf">Diploma/Setaraf</SelectItem>
                                <SelectItem value="ijazah_ke_atas">Ijazah ke Atas</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="wife_pekerjaan">Pekerjaan</Label>
                        <Select
                            value={data.wife.pekerjaan}
                            onValueChange={(value) => {
                                setData('wife', {
                                    ...data.wife,
                                    pekerjaan: value,
                                    pekerjaan_lain: value !== 'lain_lain' ? '' : data.wife.pekerjaan_lain
                                });
                            }}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Pilih pekerjaan" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="penjawat_awam">Penjawat Awam</SelectItem>
                                <SelectItem value="pekerja_swasta">Pekerja Swasta</SelectItem>
                                <SelectItem value="petani">Petani</SelectItem>
                                <SelectItem value="nelayan">Nelayan</SelectItem>
                                <SelectItem value="usahawan">Usahawan</SelectItem>
                                <SelectItem value="surirumah_sepenuh_masa">Surirumah Sepenuh Masa</SelectItem>
                                <SelectItem value="pesara">Pesara</SelectItem>
                                <SelectItem value="penganggur_belia">Penganggur - Belia (Tiada Kemahiran)</SelectItem>
                                <SelectItem value="penganggur_graduan">Penganggur - Graduan</SelectItem>
                                <SelectItem value="lain_lain">Lain-lain (Nyatakan)</SelectItem>
                            </SelectContent>
                        </Select>
                        {data.wife.pekerjaan === 'lain_lain' && (
                            <Input
                                id="wife_pekerjaan_lain"
                                value={data.wife.pekerjaan_lain}
                                onChange={e => setData('wife', {
                                    ...data.wife,
                                    pekerjaan_lain: e.target.value
                                })}
                                placeholder="Nyatakan pekerjaan lain"
                                className="mt-2"
                            />
                        )}
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="wife_agama">Agama</Label>
                        <Select
                            value={data.wife.agama}
                            onValueChange={(value) => {
                                setData('wife', {
                                    ...data.wife,
                                    agama: value,
                                    agama_lain: value !== 'lain-lain' ? '' : data.wife.agama_lain
                                });
                            }}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Pilih agama" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="islam">Islam</SelectItem>
                                <SelectItem value="kristian">Kristian</SelectItem>
                                <SelectItem value="buddha">Buddha</SelectItem>
                                <SelectItem value="hindu">Hindu</SelectItem>
                                <SelectItem value="lain-lain">Lain-lain</SelectItem>
                            </SelectContent>
                        </Select>
                        {data.wife.agama === 'lain-lain' && (
                            <Input
                                id="wife_agama_lain"
                                value={data.wife.agama_lain}
                                onChange={e => setData('wife', {
                                    ...data.wife,
                                    agama_lain: e.target.value
                                })}
                                placeholder="Nyatakan agama lain"
                                className="mt-2"
                            />
                        )}
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="wife_bangsa">Bangsa</Label>
                        <Select
                            value={data.wife.bangsa}
                            onValueChange={(value) => setData('wife', {
                                ...data.wife,
                                bangsa: value
                            })}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Pilih bangsa" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="melayu">Melayu</SelectItem>
                                <SelectItem value="cina">Cina</SelectItem>
                                <SelectItem value="india">India</SelectItem>
                                <SelectItem value="bumiputera">Bumiputera</SelectItem>
                                <SelectItem value="lain-lain">Lain-lain</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                <div className="flex items-center space-x-2 mt-4">
                    <Checkbox
                        id="wife_status_oku"
                        checked={data.wife.status_oku}
                        onCheckedChange={(checked) => setData('wife', {
                            ...data.wife,
                            status_oku: Boolean(checked)
                        })}
                    />
                    <Label htmlFor="wife_status_oku">Status OKU</Label>
                </div>
            </div>
        </div>
    );
} 