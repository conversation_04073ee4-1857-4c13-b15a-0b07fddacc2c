<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('keluarga', function (Blueprint $table) {
            $table->id();
            $table->integer('lot');
            $table->integer('bil_rumah');
            $table->string('nama');
            $table->date('tarikh_lahir');
            $table->string('no_hp');
            $table->string('status');
            $table->string('pendidikan');
            $table->string('pekerjaan');
            $table->string('agama');
            $table->string('bangsa');
            $table->boolean('status_oku');
            $table->boolean('e-kasih');
            $table->boolean('pprt');
            $table->decimal('pendapatan', places:2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('keluarga');
    }
};
