import { useState, useEffect } from 'react';
import { Head, useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { AlertDialog, AlertDialogAction, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import KeluargaIsteriForm from './borang/keluarga-isteri';
import AnakForm from './borang/anak';

interface EditFamilyProps {
    formData: {
        lot: string;
        bil_rumah: string;
        nama: string;
        tarikh_lahir: string;
        no_hp: string;
        status: string;
        pendidikan: string;
        pekerjaan: string;
        pekerjaan_lain: string;
        agama: string;
        agama_lain: string;
        bangsa: string;
        status_oku: boolean;
        'e-kasih': boolean;
        pprt: boolean;
        pendapatan: string;
        wife: {
            nama: string;
            tarikh_lahir: string;
            no_hp: string;
            status: string;
            pendidikan: string;
            pekerjaan: string;
            pekerjaan_lain: string;
            agama: string;
            agama_lain: string;
            bangsa: string;
            status_oku: boolean;
        };
        children: Array<{
            id?: string;
            nama: string;
            jantina: string;
            tarikh_lahir: string;
            no_hp: string;
            status: string;
            pendidikan: string;
            pekerjaan: string;
            pekerjaan_lain: string;
            agama: string;
            agama_lain: string;
            status_oku: boolean;
            sedang_belajar_ipt: boolean;
            tajaan: string;
        }>;
    };
    familyId: number;
    isEditing: boolean;
}

export default function EditFamily({ formData, familyId }: EditFamilyProps) {
    const [children, setChildren] = useState(formData.children);
    const [showSuccessDialog, setShowSuccessDialog] = useState(false);
    const [showErrorDialog, setShowErrorDialog] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');

    // Form for data update
    const form = useForm(formData);

    // Calculate age from date
    const calculateAge = (dateStr: string) => {
        if (!dateStr) return '';
        const birthDate = new Date(dateStr);
        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
            age--;
        }
        return age;
    };

    // Update form data when children change
    useEffect(() => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        form.setData('children', children as any);
    }, [children]); // eslint-disable-line react-hooks/exhaustive-deps

    // Handle form submission
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        form.put(`/keluarga/${familyId}`, {
            onSuccess: () => {
                setShowSuccessDialog(true);
            },
            onError: (errors) => {
                console.error('Update errors:', errors);
                
                // Create user-friendly error message
                let message = 'Terdapat ralat semasa mengemaskini data. Sila semak maklumat yang dimasukkan.';
                
                // Get first error message
                const errorValues = Object.values(errors);
                if (errorValues.length > 0) {
                    const firstError = errorValues[0];
                    if (typeof firstError === 'string') {
                        message = firstError;
                    }
                }
                
                setErrorMessage(message);
                setShowErrorDialog(true);
            },
        });
    };

    // Handle success dialog confirm
    const handleSuccessConfirm = () => {
        setShowSuccessDialog(false);
        window.location.href = '/kemaskini';
    };

    // Handle error dialog confirm
    const handleErrorConfirm = () => {
        setShowErrorDialog(false);
        setErrorMessage('');
    };

    return (
        <>
            <Head title="Kemaskini Data Keluarga" />
            
            <div className="min-h-screen bg-gray-50 py-8 px-4">
                <div className="container mx-auto">
                    <Card className="max-w-6xl mx-auto">
                        <CardHeader className="text-center">
                            <CardTitle className="text-2xl">Kemaskini Data Keluarga</CardTitle>
                            <CardDescription>
                                Kemaskini maklumat keluarga untuk: <strong>{formData.nama}</strong>
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-8">
                                {/* Family Head and Wife Form */}
                                <KeluargaIsteriForm
                                    data={form.data}
                                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                                    setData={(key: string, value: unknown) => form.setData(key as any, value)}
                                    calculateAge={calculateAge}
                                />
                                
                                <Separator />
                                
                                {/* Children Form */}
                                <AnakForm
                                    children={children}
                                    setChildren={setChildren}
                                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                                    setData={(key: string, value: unknown) => form.setData(key as any, value)}
                                    calculateAge={calculateAge}
                                />
                                
                                <div className="flex space-x-4 pt-6">
                                    <Button type="submit" disabled={form.processing} className="flex-1">
                                        {form.processing ? 'Menyimpan...' : 'Kemaskini Data'}
                                    </Button>
                                    <Button 
                                        type="button" 
                                        variant="outline" 
                                        onClick={() => window.location.href = '/kemaskini'}
                                        className="flex-1"
                                    >
                                        Batal
                                    </Button>
                                </div>
                                
                                {Object.keys(form.errors).length > 0 && (
                                    <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                                        <h4 className="text-red-800 font-medium mb-2">Terdapat ralat dalam borang:</h4>
                                        <ul className="text-red-700 text-sm space-y-1">
                                            {Object.entries(form.errors).map(([key, error]) => (
                                                <li key={key}>• {error}</li>
                                            ))}
                                        </ul>
                                    </div>
                                )}
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>

            {/* Success Dialog */}
            <AlertDialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Berjaya!</AlertDialogTitle>
                        <AlertDialogDescription>
                            Data keluarga berjaya dikemaskini. Anda akan dibawa kembali ke halaman utama.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogAction onClick={handleSuccessConfirm}>
                            Kembali ke Halaman Utama
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Error Dialog */}
            <AlertDialog open={showErrorDialog} onOpenChange={setShowErrorDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Ralat!</AlertDialogTitle>
                        <AlertDialogDescription>
                            {errorMessage}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogAction onClick={handleErrorConfirm}>
                            Cuba Lagi
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
} 