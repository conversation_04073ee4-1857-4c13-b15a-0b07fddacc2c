<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('anak', function (Blueprint $table) {
            $table->id();
            $table->foreignId('father_id')->constrained('keluarga')->onDelete('cascade');
            $table->string('nama');
            $table->enum('jantina', ['lelaki', 'perempuan']);
            $table->date('tarikh_lahir');
            $table->string('no_hp');
            $table->string('status');
            $table->string('pendidikan');
            $table->string('pekerjaan')->nullable();
            $table->string('pekerjaan_lain')->nullable();
            $table->string('agama');
            $table->string('agama_lain')->nullable();
            $table->string('bangsa');
            $table->boolean('status_oku');
            $table->boolean('sedang_belajar_ipt')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('anak');
    }
};
