<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Isteri extends Model
{
    protected $table = 'isteri';
    protected $fillable = ['father_id', 'nama', 'tarikh_lahir', 'no_hp', 'status', 'pendidikan', 'pekerjaan', 'pekerjaan_lain', 'agama', 'agama_lain', 'bangsa', 'status_oku'];

    public function keluarga()
    {
        return $this->belongsTo(Keluarga::class, 'father_id');
    }
}
