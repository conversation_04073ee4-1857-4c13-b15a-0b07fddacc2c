<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Keluarga extends Model
{
    protected $table = 'keluarga';
    protected $fillable = ['lot', 'bil_rumah', 'nama', 'tarikh_lahir', 'no_hp', 'status', 'pendidikan', 'pekerjaan', 'pekerjaan_lain', 'agama', 'agama_lain', 'bangsa', 'status_oku', 'e-kasih', 'pprt', 'pendapatan'];

    public function isteri()
    {
        return $this->hasOne(Isteri::class, 'father_id');
    }

    public function anak()
    {
        return $this->hasMany(Anak::class, 'father_id');
    }
}
