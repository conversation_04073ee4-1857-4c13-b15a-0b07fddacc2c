import { type SharedData } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { InfoIcon, ChevronLeft, ChevronRight } from 'lucide-react';

// Import separated form components
import KeluargaIsteriForm from './borang/keluarga-isteri';
import AnakForm from './borang/anak';

type Child = {
    nama: string;
    jantina: string;
    tarikh_lahir: string;
    no_hp: string;
    status: string;
    pendidikan: string;
    pekerjaan: string;
    pekerjaan_lain: string;
    agama: string;
    agama_lain: string;
    status_oku: boolean;
    sedang_belajar_ipt: boolean;
    tajaan: string;
};

export default function Welcome() {
    const [currentStep, setCurrentStep] = useState(1);
    const [children, setChildren] = useState<Child[]>([{ 
        nama: '', 
        jantina: '', 
        tarikh_lahir: '', 
        no_hp: '', 
        status: '', 
        pendidikan: '', 
        pekerjaan: '', 
        pekerjaan_lain: '',
        agama: '', 
        agama_lain: '',
        status_oku: false,
        sedang_belajar_ipt: false,
        tajaan: ''
    }]);

    const { data, setData, post, processing, errors } = useForm({
        // Ketua Keluarga (Family Head) Data
        lot: '',
        bil_rumah: '',
        nama: '',
        tarikh_lahir: '',
        no_hp: '',
        status: '',
        pendidikan: '',
        pekerjaan: '',
        pekerjaan_lain: '',
        agama: '',
        agama_lain: '',
        bangsa: '',
        status_oku: false,
        'e-kasih': false,
        pprt: false,
        pendapatan: '',
        
        // Isteri (Wife) Data
        wife: {
            nama: '',
            tarikh_lahir: '',
            no_hp: '',
            status: '',
            pendidikan: '',
            pekerjaan: '',
            pekerjaan_lain: '',
            agama: '',
            agama_lain: '',
            bangsa: '',
            status_oku: false,
        },
        
        // Anak (Children) Data
        children: children,
    });

    const calculateAge = (dateOfBirth: string) => {
        if (!dateOfBirth) return '';
        const currentYear = new Date().getFullYear();
        const birthYear = new Date(dateOfBirth).getFullYear();
        return currentYear - birthYear;
    };

    // Validation functions for each step
    const validateKeluargaForm = (): string[] => {
        const requiredFields = [
            'lot', 'bil_rumah', 'nama', 'tarikh_lahir', 'no_hp', 
            'status', 'pendidikan', 'agama', 'bangsa', 'pendapatan'
        ];
        
        const missingFields: string[] = [];
        
        for (const field of requiredFields) {
            if (!data[field as keyof typeof data]) {
                missingFields.push(field);
            }
        }
        
        // Check pekerjaan for adults (18+)
        if (data.tarikh_lahir && Number(calculateAge(data.tarikh_lahir)) >= 18) {
            if (!data.pekerjaan) {
                missingFields.push('pekerjaan');
            } else if (data.pekerjaan === 'lain_lain' && !data.pekerjaan_lain) {
                missingFields.push('pekerjaan_lain');
            }
        }
        
        // Check custom agama field
        if (data.agama === 'lain-lain' && !data.agama_lain) {
            missingFields.push('agama_lain');
        }
        
        return missingFields;
    };

    const validateIsteriForm = (): string[] => {
        const requiredFields = [
            'nama', 'tarikh_lahir', 'no_hp', 'status', 
            'pendidikan', 'pekerjaan', 'agama', 'bangsa'
        ];
        
        const missingFields: string[] = [];
        
        for (const field of requiredFields) {
            if (!data.wife[field as keyof typeof data.wife]) {
                missingFields.push(`wife.${field}`);
            }
        }
        
        // Check pekerjaan_lain when lain_lain is selected
        if (data.wife.pekerjaan === 'lain_lain' && !data.wife.pekerjaan_lain) {
            missingFields.push('wife.pekerjaan_lain');
        }
        
        // Check custom agama field
        if (data.wife.agama === 'lain-lain' && !data.wife.agama_lain) {
            missingFields.push('wife.agama_lain');
        }
        
        return missingFields;
    };

    const validateAnakForm = (): string[] => {
        const missingFields: string[] = [];
        
        children.forEach((child, index) => {
            const requiredFields = ['nama', 'jantina', 'tarikh_lahir', 'no_hp', 'status', 'pendidikan', 'agama'];
            
            for (const field of requiredFields) {
                if (!child[field as keyof Child]) {
                    missingFields.push(`children[${index}].${field}`);
                }
            }
            
            // Check pekerjaan for adults (18+)
            if (child.tarikh_lahir && Number(calculateAge(child.tarikh_lahir)) >= 18) {
                if (!child.pekerjaan) {
                    missingFields.push(`children[${index}].pekerjaan`);
                } else if (child.pekerjaan === 'lain_lain' && !child.pekerjaan_lain) {
                    missingFields.push(`children[${index}].pekerjaan_lain`);
                }
            }
            
            // Check custom agama field
            if (child.agama === 'lain-lain' && !child.agama_lain) {
                missingFields.push(`children[${index}].agama_lain`);
            }
            
            // Check IPT fields if studying at university
            if (child.sedang_belajar_ipt && !child.tajaan) {
                missingFields.push(`children[${index}].tajaan`);
            }
        });
        
        return missingFields;
    };

    const validateCurrentStep = () => {
        switch (currentStep) {
            case 1:
                return [...validateKeluargaForm(), ...validateIsteriForm()];
            case 2:
                return validateAnakForm();
            default:
                return [];
        }
    };

    const getFieldDisplayName = (fieldPath: string) => {
        const fieldMap: { [key: string]: string } = {
            'lot': 'Lot',
            'bil_rumah': 'Bil Rumah',
            'nama': 'Nama',
            'tarikh_lahir': 'Tarikh Lahir',
            'no_hp': 'Nombor Telefon',
            'status': 'Status',
            'pendidikan': 'Tahap Pendidikan',
            'pekerjaan': 'Pekerjaan',
            'pekerjaan_lain': 'Pekerjaan Lain',
            'agama': 'Agama',
            'agama_lain': 'Agama Lain',
            'bangsa': 'Bangsa',
            'pendapatan': 'Pendapatan',
            'jantina': 'Jantina',
            'tajaan': 'Jenis Tajaan IPT'
        };
        
        // Handle nested fields
        if (fieldPath.includes('wife.')) {
            const field = fieldPath.replace('wife.', '');
            return `Isteri - ${fieldMap[field] || field}`;
        }
        
        if (fieldPath.includes('children[')) {
            const match = fieldPath.match(/children\[(\d+)\]\.(.+)/);
            if (match) {
                const childIndex = parseInt(match[1]) + 1;
                const field = match[2];
                return `Anak ${childIndex} - ${fieldMap[field] || field}`;
            }
        }
        
        return fieldMap[fieldPath] || fieldPath;
    };

    const nextStep = () => {
        const missingFields = validateCurrentStep();
        
        if (missingFields.length > 0) {
            const fieldNames = missingFields.map(getFieldDisplayName).join(', ');
            alert(`Sila lengkapkan medan berikut sebelum meneruskan:\n\n${fieldNames}`);
            return;
        }
        
        if (currentStep < 2) {
            setCurrentStep(currentStep + 1);
        }
    };

    const prevStep = () => {
        if (currentStep > 1) {
            setCurrentStep(currentStep - 1);
        }
    };

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('keluarga.store'), {
            preserveScroll: true,
        });
    };

    const getStepTitle = () => {
        switch (currentStep) {
            case 1:
                return "Maklumat Ketua Keluarga & Isteri";
            case 2:
                return "Maklumat Anak";
            default:
                return "";
        }
    };

    const getStepDescription = () => {
        switch (currentStep) {
            case 1:
                return "Sila isi maklumat ketua keluarga dan isteri dengan lengkap";
            case 2:
                return "Sila isi maklumat anak-anak anda dengan lengkap";
            default:
                return "";
        }
    };

    const renderStepIndicator = () => {
        return (
            <div className="flex justify-center mb-8">
                <div className="flex items-center space-x-4">
                    {[1, 2].map((step) => (
                        <div key={step} className="flex items-center">
                            <div
                                className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                                    step === currentStep
                                        ? 'bg-primary text-primary-foreground'
                                        : step < currentStep
                                        ? 'bg-primary/20 text-primary'
                                        : 'bg-muted text-muted-foreground'
                                }`}
                            >
                                {step}
                            </div>
                            {step < 2 && (
                                <div
                                    className={`w-16 h-1 mx-2 ${
                                        step < currentStep ? 'bg-primary' : 'bg-muted'
                                    }`}
                                />
                            )}
                        </div>
                    ))}
                </div>
            </div>
        );
    };

    const renderCurrentStep = () => {
        switch (currentStep) {
            case 1:
                return (
                    <KeluargaIsteriForm 
                        data={data} 
                        setData={setData} 
                        calculateAge={calculateAge} 
                    />
                );
            case 2:
                return (
                    <AnakForm
                        children={data.children}
                        setChildren={(newChildren) => {
                            setChildren(newChildren);
                            setData('children', newChildren);
                        }}
                        setData={setData}
                        calculateAge={calculateAge}
                    />
                );
            default:
                return (
                    <KeluargaIsteriForm 
                        data={data} 
                        setData={setData} 
                        calculateAge={calculateAge} 
                    />
                );
        }
    };

    const renderNavigationButtons = () => (
        <div className="flex justify-between items-center mt-8">
            <Button
                type="button"
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 1}
                className="flex items-center gap-2"
            >
                <ChevronLeft className="h-4 w-4" />
                Kembali
            </Button>

            <Button type="button" variant="outline" onClick={() => window.location.reload()}>
                Set Semula
            </Button>

            {currentStep < 2 ? (
                <Button
                    type="button"
                    onClick={nextStep}
                    className="flex items-center gap-2"
                >
                    Seterusnya
                    <ChevronRight className="h-4 w-4" />
                </Button>
            ) : (
                <Button type="submit" disabled={processing} onClick={submit}>
                    {processing ? 'Sedang Menghantar...' : 'Hantar Borang'}
                </Button>
            )}
        </div>
    );

    return (
        <div className="container mx-auto py-10">
            <Head title="Sistem Profil Kampung Di Peringkat 5" />
            
            <Card className="max-w-4xl mx-auto">
                <CardHeader>
                    <CardTitle className="text-2xl font-bold text-center">Sistem Profil Kampung Di Peringkat 5</CardTitle>
                    <CardDescription className="text-center text-base">
                        Sila isi borang ini dengan lengkap untuk mendaftarkan profil keluarga anda.
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <Alert className="mb-6">
                        <InfoIcon className="h-4 w-4" />
                        <AlertDescription>
                            Sila pastikan semua maklumat yang diisi adalah tepat dan lengkap. Maklumat ini akan digunakan untuk tujuan rasmi. 
                        </AlertDescription>
                        <AlertDescription>
                            Disediakan oleh : Unit Perancang Ekonomi Negeri Sabah
                        </AlertDescription>
                        
                    </Alert>

                    {renderStepIndicator()}

                    <form className="space-y-8" onSubmit={submit}>
                        <div className="bg-primary/5 p-4 rounded-lg">
                            <h3 className="text-lg font-semibold mb-2">{getStepTitle()}</h3>
                            <p className="text-sm text-muted-foreground mb-4">
                                {getStepDescription()}
                            </p>
                        </div>

                        {renderCurrentStep()}
                        {renderNavigationButtons()}
                    </form>
                </CardContent>
            </Card>
        </div>
    );
}
